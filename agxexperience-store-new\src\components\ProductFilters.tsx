'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface ProductFiltersProps {
  categories: string[];
  priceRange: { min: number; max: number };
  onFiltersChange: (filters: FilterState) => void;
  initialFilters?: FilterState;
  totalCount: number;
}

export interface FilterState {
  category: string;
  search: string;
  minPrice: number;
  maxPrice: number;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

export default function ProductFilters({
  categories,
  priceRange,
  onFiltersChange,
  initialFilters,
  totalCount
}: ProductFiltersProps) {
  const [filters, setFilters] = useState<FilterState>({
    category: 'all',
    search: '',
    minPrice: priceRange.min,
    maxPrice: priceRange.max,
    sortBy: 'created_at',
    sortOrder: 'desc',
    ...initialFilters
  });

  const [activeSection, setActiveSection] = useState<string | null>(null);
  const [searchDebounce, setSearchDebounce] = useState<NodeJS.Timeout | null>(null);
  const [showCategories, setShowCategories] = useState(false);
  const [showPriceRange, setShowPriceRange] = useState(false);

  useEffect(() => {
    setFilters(prev => ({
      ...prev,
      minPrice: priceRange.min,
      maxPrice: priceRange.max
    }));
  }, [priceRange]);

  const updateFilters = (newFilters: Partial<FilterState>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    onFiltersChange(updatedFilters);
  };

  const handleSearchChange = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
    
    // Debounce search
    if (searchDebounce) clearTimeout(searchDebounce);
    
    const timeout = setTimeout(() => {
      updateFilters({ search: value });
    }, 500);
    
    setSearchDebounce(timeout);
  };

  const resetFilters = () => {
    const resetState: FilterState = {
      category: 'all',
      search: '',
      minPrice: priceRange.min,
      maxPrice: priceRange.max,
      sortBy: 'created_at',
      sortOrder: 'desc'
    };
    setFilters(resetState);
    onFiltersChange(resetState);
  };

  const getCategoryDisplayName = (category: string) => {
    const categoryMap: { [key: string]: string } = {
      'ai': 'Intelligenza Artificiale',
      'automation': 'Automazione',
      'web-design': 'Web Design',
      'telegram': 'Telegram Bot',
      'chatbot': 'Chatbot',
      'portfolio': 'Portfolio',
      'notion': 'Notion',
      'productivity': 'Produttività',
      'integration': 'Integrazioni',
      'seo': 'SEO',
      'cms': 'CMS',
      'landing-page': 'Landing Page',
      'workflow': 'Workflow',
      'google-sheets': 'Google Sheets',
      'customer-service': 'Customer Service',
      'nlp': 'Natural Language Processing',
      'responsive': 'Responsive Design'
    };
    return categoryMap[category] || category.charAt(0).toUpperCase() + category.slice(1);
  };

  return (
    <div className="space-y-4">
      {/* Header con contatore risultati */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-white">Filtri</h2>
          <p className="text-sm text-gray-400">{totalCount} prodotti</p>
        </div>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={resetFilters}
          className="text-xs text-purple-400 hover:text-purple-300 transition-colors"
        >
          Reset
        </motion.button>
      </div>

      {/* Barra di ricerca compatta */}
      <div className="relative">
        <input
          type="text"
          value={filters.search}
          onChange={(e) => handleSearchChange(e.target.value)}
          placeholder="Cerca prodotti..."
          className="w-full glass-strong border border-white/10 rounded-lg px-4 py-2.5 pl-10 text-sm text-white placeholder-white/50 focus:outline-none focus:border-purple-500/50 transition-colors"
        />
        <svg
          className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/50"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>

      {/* Dropdown Categorie */}
      <div className="relative">
        <motion.button
          whileHover={{ scale: 1.01 }}
          whileTap={{ scale: 0.99 }}
          onClick={() => setShowCategories(!showCategories)}
          className="w-full glass-strong border border-white/10 rounded-lg px-4 py-2.5 text-sm text-white flex items-center justify-between hover:border-purple-500/30 transition-colors"
        >
          <span className="flex items-center">
            <svg className="w-4 h-4 mr-2 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
            </svg>
            {filters.category === 'all' ? 'Tutte le categorie' : getCategoryDisplayName(filters.category)}
          </span>
          <motion.svg
            animate={{ rotate: showCategories ? 180 : 0 }}
            className="w-4 h-4 text-white/60"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </motion.svg>
        </motion.button>

        <AnimatePresence>
          {showCategories && (
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute top-full left-0 right-0 mt-2 glass-strong border border-white/10 rounded-lg shadow-xl z-50 max-h-64 overflow-y-auto"
            >
              <div className="p-2">
                <motion.button
                  whileHover={{ backgroundColor: 'rgba(139, 92, 246, 0.1)' }}
                  onClick={() => {
                    updateFilters({ category: 'all' });
                    setShowCategories(false);
                  }}
                  className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                    filters.category === 'all' ? 'text-purple-400 bg-purple-500/10' : 'text-gray-300 hover:text-white'
                  }`}
                >
                  Tutte le categorie
                </motion.button>
                {categories.map((category) => (
                  <motion.button
                    key={category}
                    whileHover={{ backgroundColor: 'rgba(139, 92, 246, 0.1)' }}
                    onClick={() => {
                      updateFilters({ category });
                      setShowCategories(false);
                    }}
                    className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                      filters.category === category ? 'text-purple-400 bg-purple-500/10' : 'text-gray-300 hover:text-white'
                    }`}
                  >
                    {getCategoryDisplayName(category)}
                  </motion.button>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Range Prezzi Compatto */}
      <div>
        <motion.button
          whileHover={{ scale: 1.01 }}
          whileTap={{ scale: 0.99 }}
          onClick={() => setShowPriceRange(!showPriceRange)}
          className="w-full glass-strong border border-white/10 rounded-lg px-4 py-2.5 text-sm text-white flex items-center justify-between hover:border-purple-500/30 transition-colors"
        >
          <span className="flex items-center">
            <svg className="w-4 h-4 mr-2 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
            €{filters.minPrice} - €{filters.maxPrice}
          </span>
          <motion.svg
            animate={{ rotate: showPriceRange ? 180 : 0 }}
            className="w-4 h-4 text-white/60"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </motion.svg>
        </motion.button>

        <AnimatePresence>
          {showPriceRange && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-3 glass-strong border border-white/10 rounded-lg p-4"
            >
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-xs text-gray-400 mb-2">
                    <span>Min: €{filters.minPrice}</span>
                    <span>Max: €{filters.maxPrice}</span>
                  </div>
                  <input
                    type="range"
                    min={priceRange.min}
                    max={priceRange.max}
                    value={filters.minPrice}
                    onChange={(e) => updateFilters({ minPrice: Number(e.target.value) })}
                    className="w-full slider"
                  />
                </div>
                <div>
                  <input
                    type="range"
                    min={priceRange.min}
                    max={priceRange.max}
                    value={filters.maxPrice}
                    onChange={(e) => updateFilters({ maxPrice: Number(e.target.value) })}
                    className="w-full slider"
                  />
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Ordinamento Compatto */}
      <div className="grid grid-cols-2 gap-2">
        <select
          value={filters.sortBy}
          onChange={(e) => updateFilters({ sortBy: e.target.value })}
          className="glass-strong border border-white/10 rounded-lg px-3 py-2 text-sm text-white bg-transparent focus:outline-none focus:border-purple-500/50 transition-colors"
        >
          <option value="created_at" className="bg-gray-800">Data</option>
          <option value="name" className="bg-gray-800">Nome</option>
          <option value="price" className="bg-gray-800">Prezzo</option>
        </select>
        <select
          value={filters.sortOrder}
          onChange={(e) => updateFilters({ sortOrder: e.target.value as 'asc' | 'desc' })}
          className="glass-strong border border-white/10 rounded-lg px-3 py-2 text-sm text-white bg-transparent focus:outline-none focus:border-purple-500/50 transition-colors"
        >
          <option value="desc" className="bg-gray-800">↓</option>
          <option value="asc" className="bg-gray-800">↑</option>
        </select>
      </div>

      {/* Filtri Attivi */}
      {(filters.category !== 'all' || filters.search || filters.minPrice > priceRange.min || filters.maxPrice < priceRange.max) && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-wrap gap-2"
        >
          {filters.category !== 'all' && (
            <motion.span
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="inline-flex items-center px-2 py-1 bg-purple-500/20 text-purple-300 text-xs rounded-full border border-purple-500/30"
            >
              {getCategoryDisplayName(filters.category)}
              <button
                onClick={() => updateFilters({ category: 'all' })}
                className="ml-1 hover:text-white transition-colors"
              >
                ×
              </button>
            </motion.span>
          )}
          {filters.search && (
            <motion.span
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="inline-flex items-center px-2 py-1 bg-blue-500/20 text-blue-300 text-xs rounded-full border border-blue-500/30"
            >
              "{filters.search}"
              <button
                onClick={() => updateFilters({ search: '' })}
                className="ml-1 hover:text-white transition-colors"
              >
                ×
              </button>
            </motion.span>
          )}
          {(filters.minPrice > priceRange.min || filters.maxPrice < priceRange.max) && (
            <motion.span
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="inline-flex items-center px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded-full border border-green-500/30"
            >
              €{filters.minPrice}-€{filters.maxPrice}
              <button
                onClick={() => updateFilters({ minPrice: priceRange.min, maxPrice: priceRange.max })}
                className="ml-1 hover:text-white transition-colors"
              >
                ×
              </button>
            </motion.span>
          )}
        </motion.div>
      )}
    </div>
  );
}

// Product Types
export interface Product {
  _id?: string;
  name: string;
  description: string;
  price: number;
  slug: string;
  image_url: string;
  tags: string[];
  created_at: Date;
}

// User Types
export interface User {
  _id?: string;
  email: string;
  password_hash: string;
  role: 'admin' | 'user';
  created_at: Date;
}

// Message Types
export interface Message {
  _id?: string;
  session_id: string;
  user_message: string;
  ai_response: string;
  timestamp: Date;
  emotion_state: EmotionState;
}

// Order Types
export interface Order {
  _id?: string;
  product_id: string;
  stripe_session_id: string;
  status: 'pending' | 'completed' | 'failed';
  created_at: Date;
}

// User Profile Types
export interface UserProfile {
  _id?: string;
  session_id: string;
  interests: string[];
  past_requests: PastRequest[];
  preferences: UserPreferences;
  interaction_count: number;
  last_seen: Date;
  emotional_profile: EmotionalProfile;
}

export interface PastRequest {
  request: string;
  timestamp: Date;
  response_satisfaction?: number;
}

export interface UserPreferences {
  communication_style: 'formal' | 'casual';
  product_categories: string[];
  price_range: 'premium' | 'budget' | 'any';
}

export interface EmotionalProfile {
  current_mood: 'curious' | 'excited' | 'focused' | 'discovery';
  engagement_level: number; // 0-1
  conversation_depth: 'surface' | 'detailed';
}

// Emotion State Types
export type EmotionState = 'curiosity' | 'excitement' | 'focus' | 'discovery';

// AI Chat Types
export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  emotion_state?: EmotionState;
}

export interface ChatSession {
  session_id: string;
  messages: ChatMessage[];
  user_profile?: UserProfile;
}

// Audio Types
export interface AudioFeatures {
  voice_frequency_analysis: boolean;
  real_time_visualization: boolean;
  background_adaptation: boolean;
  emotional_resonance: boolean;
  frequency: number; // 432Hz
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Stripe Types
export interface CheckoutSession {
  product_id: string;
  success_url: string;
  cancel_url: string;
}

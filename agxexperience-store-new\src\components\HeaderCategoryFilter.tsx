'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface HeaderCategoryFilterProps {
  categories: string[];
  activeCategory: string;
  onCategoryChange: (category: string) => void;
}

export default function HeaderCategoryFilter({
  categories,
  activeCategory,
  onCategoryChange
}: HeaderCategoryFilterProps) {
  const [isOpen, setIsOpen] = useState(false);

  // Ordina categorie per popolarità (prime 7 più ricercate)
  const sortedCategories = [...categories].sort((a, b) => {
    const popularOrder = ['all', 'telegram', 'portfolio', 'notion', 'automation', 'ai', 'design'];
    const aIndex = popularOrder.indexOf(a);
    const bIndex = popularOrder.indexOf(b);

    if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
    if (aIndex !== -1) return -1;
    if (bIndex !== -1) return 1;
    return a.localeCompare(b);
  });

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'all': return '🌟';
      case 'telegram': return '🤖';
      case 'portfolio': return '🌐';
      case 'notion': return '⚡';
      case 'automation': return '🔧';
      default: return '💎';
    }
  };

  const getCategoryLabel = (category: string) => {
    if (category === 'all') return 'Tutti';
    return category.charAt(0).toUpperCase() + category.slice(1);
  };

  return (
    <>
      {/* Blur Overlay quando dropdown è aperto */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-30"
            onClick={() => setIsOpen(false)}
          />
        )}
      </AnimatePresence>

      <div className="relative">
      <motion.button
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-1.5 glass border border-white/20 rounded-md text-white hover:bg-white/10 transition-all duration-300"
        style={{ minWidth: '100px' }}
      >
        <span className="text-xs">{getCategoryIcon(activeCategory)}</span>
        <span className="text-xs font-medium">{getCategoryLabel(activeCategory)}</span>
        <motion.svg
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.2 }}
          className="w-3 h-3 text-white/60"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </motion.svg>
      </motion.button>

      {/* Dropdown Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 mt-2 w-full min-w-[140px] glass-strong border border-white/20 rounded-lg shadow-2xl z-40 overflow-hidden"
          >
            <div className="max-h-[280px] overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent">
              {/* Indicatore scroll se più di 7 categorie */}
              {sortedCategories.length > 7 && (
                <div className="sticky top-0 bg-black/20 backdrop-blur-sm px-3 py-1 text-xs text-white/60 text-center border-b border-white/10">
                  {sortedCategories.length} categorie • Scorri per vedere tutte
                </div>
              )}
              {sortedCategories.map((category, index) => (
                <div key={category}>
                  {/* Separatore dopo le prime 7 categorie */}
                  {index === 7 && sortedCategories.length > 7 && (
                    <div className="border-t border-white/10 my-1">
                      <div className="px-3 py-1 text-xs text-white/40 text-center bg-black/10">
                        Altre categorie
                      </div>
                    </div>
                  )}
                  <motion.button
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
                    onClick={() => {
                      onCategoryChange(category);
                      setIsOpen(false);
                    }}
                    className={`w-full flex items-center space-x-2 px-3 py-2 text-left transition-all duration-200 ${
                      activeCategory === category
                        ? 'bg-white/10 border-r-2'
                        : 'hover:bg-white/5'
                    }`}
                    style={{
                      borderRightColor: activeCategory === category ? 'var(--cta)' : 'transparent'
                    }}
                  >
                <span className="text-sm">{getCategoryIcon(category)}</span>
                <div className="flex-1">
                  <div className="text-xs font-medium text-white">
                    {getCategoryLabel(category)}
                  </div>
                </div>
                {activeCategory === category && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="w-1.5 h-1.5 rounded-full"
                    style={{ backgroundColor: 'var(--cta)' }}
                  />
                )}
                  </motion.button>
                </div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      </div>
    </>
  );
}

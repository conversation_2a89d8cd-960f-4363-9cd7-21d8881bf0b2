'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import AuroraAvatar from '@/components/AuroraAvatar';

export default function SuccessPage() {
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session_id');
  const [orderStatus, setOrderStatus] = useState<'loading' | 'success' | 'error'>('loading');

  useEffect(() => {
    if (sessionId) {
      // Update order status in database
      updateOrderStatus();
    } else {
      setOrderStatus('error');
    }
  }, [sessionId]);

  const updateOrderStatus = async () => {
    try {
      const response = await fetch('/api/checkout', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session_id: sessionId,
          status: 'completed'
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        setOrderStatus('success');
      } else {
        setOrderStatus('error');
      }
    } catch (error) {
      console.error('Error updating order status:', error);
      setOrderStatus('error');
    }
  };

  if (orderStatus === 'loading') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  if (orderStatus === 'error') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-400 mb-4">Errore</h1>
          <p className="text-gray-300 mb-6">Si è verificato un errore durante la verifica del pagamento.</p>
          <Link href="/products" className="btn-aurora">
            Torna ai Prodotti
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center">
      <div className="max-w-2xl mx-auto px-6 text-center">
        {/* Success Animation */}
        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.8, type: "spring", stiffness: 200 }}
          className="mb-8"
        >
          <AuroraAvatar 
            emotionState="excitement"
            isListening={false}
            isSpeaking={false}
            size="xl"
          />
        </motion.div>

        {/* Success Message */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="space-y-6"
        >
          <div className="space-y-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.5, type: "spring", stiffness: 300 }}
              className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6"
            >
              <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
              </svg>
            </motion.div>

            <h1 className="text-4xl lg:text-5xl font-bold text-white mb-4">
              Pagamento <span className="text-aurora">Completato!</span>
            </h1>
            
            <p className="text-xl text-gray-300 leading-relaxed">
              Grazie per il tuo acquisto! Il tuo ordine è stato elaborato con successo.
            </p>
          </div>

          {/* Order Details */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="card-aurora"
          >
            <h3 className="text-xl font-bold text-white mb-4">Cosa succede ora?</h3>
            <div className="space-y-4 text-left">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-white text-sm font-bold">1</span>
                </div>
                <div>
                  <h4 className="text-white font-medium">Conferma via Email</h4>
                  <p className="text-gray-300 text-sm">Riceverai una email di conferma con tutti i dettagli dell'ordine.</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-white text-sm font-bold">2</span>
                </div>
                <div>
                  <h4 className="text-white font-medium">Preparazione</h4>
                  <p className="text-gray-300 text-sm">Il nostro team inizierà immediatamente a lavorare sul tuo progetto.</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-white text-sm font-bold">3</span>
                </div>
                <div>
                  <h4 className="text-white font-medium">Consegna</h4>
                  <p className="text-gray-300 text-sm">Ti contatteremo entro 24-48 ore per i prossimi passi.</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* AURORA Message */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
            className="card-aurora bg-gradient-to-r from-purple-600/20 to-blue-600/20"
          >
            <div className="flex items-center space-x-4 mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white font-bold">A</span>
              </div>
              <div>
                <h4 className="text-white font-bold">Messaggio da AURORA</h4>
                <p className="text-purple-300 text-sm">La tua AI Designer</p>
              </div>
            </div>
            <p className="text-gray-300 italic">
              "Sono entusiasta di lavorare con te! Il tuo progetto sarà realizzato con la massima cura e attenzione ai dettagli. 
              Se hai domande, sono sempre qui per aiutarti. ✨"
            </p>
          </motion.div>

          {/* Actions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1 }}
            className="flex flex-col sm:flex-row gap-4 pt-6"
          >
            <Link href="/" className="btn-aurora flex-1">
              Torna alla Home
            </Link>
            <Link 
              href="/products" 
              className="flex-1 px-6 py-3 bg-gray-700/50 text-white text-center rounded-lg hover:bg-gray-600/50 transition-colors"
            >
              Altri Prodotti
            </Link>
          </motion.div>

          {/* Support */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.2 }}
            className="pt-8 border-t border-gray-800/50"
          >
            <p className="text-gray-400 text-sm">
              Hai bisogno di aiuto? Contattaci a{' '}
              <a href="mailto:<EMAIL>" className="text-aurora hover:underline">
                <EMAIL>
              </a>
            </p>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}

'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface AdvancedCategoryFilterProps {
  categories: string[];
  activeCategory: string;
  onCategoryChange: (category: string) => void;
  productCount: number;
}

export default function AdvancedCategoryFilter({ 
  categories, 
  activeCategory, 
  onCategoryChange,
  productCount
}: AdvancedCategoryFilterProps) {
  const [isOpen, setIsOpen] = useState(false);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'all': return '🌟';
      case 'telegram': return '🤖';
      case 'portfolio': return '🌐';
      case 'notion': return '⚡';
      case 'automation': return '🔧';
      default: return '💎';
    }
  };

  const getCategoryLabel = (category: string) => {
    if (category === 'all') return 'Tutti i Prodotti';
    return category.charAt(0).toUpperCase() + category.slice(1);
  };

  return (
    <div className="mb-2">
      <div className="flex items-center justify-between">
        {/* Dropdown Avanzato */}
        <div className="relative">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setIsOpen(!isOpen)}
            className="flex items-center space-x-1.5 px-3 py-1.5 glass border border-white/20 rounded-md text-white hover:bg-white/10 transition-all duration-300"
            style={{ minWidth: '120px' }}
          >
            <span className="text-xs">{getCategoryIcon(activeCategory)}</span>
            <span className="text-xs font-medium">{getCategoryLabel(activeCategory)}</span>
            <motion.svg
              animate={{ rotate: isOpen ? 180 : 0 }}
              transition={{ duration: 0.2 }}
              className="w-3 h-3 text-white/60"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </motion.svg>
          </motion.button>

          {/* Dropdown Menu */}
          <AnimatePresence>
            {isOpen && (
              <motion.div
                initial={{ opacity: 0, y: -10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -10, scale: 0.95 }}
                transition={{ duration: 0.2 }}
                className="absolute top-full left-0 mt-2 w-full glass-strong border border-white/20 rounded-lg shadow-2xl z-50 overflow-hidden"
              >
                {categories.map((category, index) => (
                  <motion.button
                    key={category}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
                    onClick={() => {
                      onCategoryChange(category);
                      setIsOpen(false);
                    }}
                    className={`w-full flex items-center space-x-2 px-3 py-2 text-left transition-all duration-200 ${
                      activeCategory === category
                        ? 'bg-white/10 border-r-2'
                        : 'hover:bg-white/5'
                    }`}
                    style={{
                      borderRightColor: activeCategory === category ? 'var(--cta)' : 'transparent'
                    }}
                  >
                    <span className="text-sm">{getCategoryIcon(category)}</span>
                    <div className="flex-1">
                      <div className="text-xs font-medium text-white">
                        {getCategoryLabel(category)}
                      </div>
                      {category !== 'all' && (
                        <div className="text-xs text-white/60">
                          Specializzata
                        </div>
                      )}
                    </div>
                    {activeCategory === category && (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="w-1.5 h-1.5 rounded-full"
                        style={{ backgroundColor: 'var(--cta)' }}
                      />
                    )}
                  </motion.button>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Info Prodotti - Ridotte 35% */}
        <div className="flex items-center space-x-3">
          <div className="text-xs text-white/60">
            <span className="font-medium text-white text-xs">{productCount}</span>
            <span className="text-xs"> prodotti</span>
          </div>

          {/* Indicatore Categoria Attiva */}
          <div className="flex items-center space-x-1.5">
            <div
              className="w-1.5 h-1.5 rounded-full"
              style={{ backgroundColor: 'var(--cta)' }}
            />
            <span className="text-xs text-white/80">
              {activeCategory === 'all' ? 'Tutti' : activeCategory}
            </span>
          </div>
        </div>
      </div>

      {/* Linea Decorativa - Ridotta 35% */}
      <motion.div
        initial={{ scaleX: 0 }}
        animate={{ scaleX: 1 }}
        transition={{ delay: 0.3, duration: 0.6 }}
        className="mt-2 h-px w-full"
        style={{
          background: 'linear-gradient(90deg, transparent, var(--accenti), transparent)',
          transformOrigin: 'center'
        }}
      />
    </div>
  );
}

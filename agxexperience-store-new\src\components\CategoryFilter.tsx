'use client';



interface CategoryFilterProps {
  categories: string[];
  activeCategory: string;
  onCategoryChange: (category: string) => void;
}

export default function CategoryFilter({ 
  categories, 
  activeCategory, 
  onCategoryChange 
}: CategoryFilterProps) {
  
  return (
    <div className="mb-2">
      {/* Filtri Compatti e Intelligenti */}
      <div className="flex items-center justify-between">
        {/* Dropdown Categorie */}
        <div className="relative">
          <select
            value={activeCategory}
            onChange={(e) => onCategoryChange(e.target.value)}
            className="appearance-none bg-black/40 backdrop-blur-sm border border-white/20 rounded-md px-3 py-1 text-xs text-white focus:outline-none focus:border-purple-500/50 pr-8"
            style={{ minWidth: '100px' }}
          >
            {categories.map((category) => (
              <option key={category} value={category} className="bg-black text-white">
                {category === 'all' ? 'Tutti' : category.charAt(0).toUpperCase() + category.slice(1)}
              </option>
            ))}
          </select>
          {/* Icona dropdown */}
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none">
            <svg className="w-3 h-3 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>

        {/* Contatore Prodotti */}
        <div className="text-xs text-white/60">
          {categories.length - 1} categorie
        </div>
      </div>
    </div>
  );
}

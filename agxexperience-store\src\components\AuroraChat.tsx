'use client';

import { useState, useEffect, useRef } from 'react';
import { ChatMessage, EmotionState, Product } from '@/types';

interface AuroraChatProps {
  onProductSuggestion?: (productId: string) => void;
}

export default function AuroraChat({ onProductSuggestion }: AuroraChatProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId] = useState(() => `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const [emotionState, setEmotionState] = useState<EmotionState>('curiosity');
  const [products, setProducts] = useState<Product[]>([]);
  const [showProducts, setShowProducts] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Initial AURORA greeting
    const initialMessage: ChatMessage = {
      role: 'assistant',
      content: "Ciao! Sono AURORA, la tua designer personale AI. Cosa posso creare per te oggi? ✨",
      timestamp: new Date(),
      emotion_state: 'curiosity'
    };
    setMessages([initialMessage]);

    // Load products
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      const response = await fetch('/api/products');
      const data = await response.json();
      if (data.success) {
        setProducts(data.data);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const sendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      role: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: inputValue,
          session_id: sessionId
        }),
      });

      const data = await response.json();

      if (data.success) {
        const aiMessage: ChatMessage = {
          role: 'assistant',
          content: data.data.response,
          timestamp: new Date(),
          emotion_state: data.data.emotion_state
        };

        setMessages(prev => [...prev, aiMessage]);
        setEmotionState(data.data.emotion_state);

        // Show products after a few messages
        if (messages.length >= 2 && !showProducts) {
          setTimeout(() => setShowProducts(true), 1000);
        }
      } else {
        throw new Error(data.error);
      }
    } catch (error) {
      console.error('Chat error:', error);
      const errorMessage: ChatMessage = {
        role: 'assistant',
        content: "Mi dispiace, ho avuto un problema tecnico. Puoi riprovare?",
        timestamp: new Date(),
        emotion_state: 'focus'
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const getEmotionColors = (emotion: EmotionState) => {
    switch (emotion) {
      case 'curiosity':
        return 'from-blue-500/20 to-purple-500/20';
      case 'excitement':
        return 'from-yellow-500/20 to-orange-500/20';
      case 'focus':
        return 'from-gray-500/20 to-slate-500/20';
      case 'discovery':
        return 'from-green-500/20 to-teal-500/20';
      default:
        return 'from-purple-500/20 to-blue-500/20';
    }
  };

  return (
    <div className="flex flex-col h-full max-w-4xl mx-auto">
      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto p-6 space-y-4">
        <AnimatePresence>
          {messages.map((message, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl backdrop-blur-sm border ${
                  message.role === 'user'
                    ? 'bg-gradient-to-r from-blue-500/10 to-purple-500/10 border-blue-500/20 text-white'
                    : `bg-gradient-to-r ${getEmotionColors(message.emotion_state || 'curiosity')} border-purple-500/20 text-white`
                }`}
              >
                {message.role === 'assistant' && (
                  <div className="flex items-center mb-2">
                    <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse mr-2"></div>
                    <span className="text-xs text-purple-300 font-medium">AURORA</span>
                  </div>
                )}
                <p className="text-sm leading-relaxed">{message.content}</p>
                <div className="text-xs text-gray-400 mt-1">
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>

        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex justify-start"
          >
            <div className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 border border-purple-500/20 rounded-2xl px-4 py-3 backdrop-blur-sm">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                <span className="text-xs text-purple-300 font-medium">AURORA</span>
              </div>
              <div className="flex space-x-1 mt-2">
                <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </motion.div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Products Section */}
      {showProducts && products.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="p-6 border-t border-gray-800/50"
        >
          <ProductGrid products={products} title="Ecco cosa posso creare per te" />
        </motion.div>
      )}

      {/* Input Area */}
      <div className="p-6 border-t border-gray-800/50">
        <div className="flex space-x-4">
          <div className="flex-1 relative">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Dimmi cosa desideri creare..."
              className="w-full bg-gray-900/50 border border-gray-700/50 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-1 focus:ring-purple-500/50 backdrop-blur-sm resize-none"
              rows={1}
              disabled={isLoading}
            />
          </div>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={sendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-xl font-medium disabled:opacity-50 disabled:cursor-not-allowed hover:from-purple-700 hover:to-blue-700 transition-all duration-200"
          >
            Invia
          </motion.button>
        </div>
      </div>
    </div>
  );
}

// Test script per verificare le API del progetto AGXexperience Store
const http = require('http');
const url = require('url');

// Mock delle API per testing
const mockProducts = [
  {
    _id: '1',
    name: "Bot Telegram AI",
    description: "Assistente AI personalizzato per Telegram che automatizza le tue conversazioni e gestisce le richieste dei clienti 24/7.",
    price: 299,
    slug: "bot-telegram-ai",
    image_url: "/images/telegram-bot.jpg",
    tags: ["ai", "automation", "telegram", "chatbot", "customer-service"],
    created_at: new Date()
  },
  {
    _id: '2',
    name: "Landing Page Portfolio",
    description: "Sito vetrina professionale responsive con design moderno, ottimizzato SEO e integrazione CMS.",
    price: 599,
    slug: "landing-page-portfolio",
    image_url: "/images/portfolio-landing.jpg", 
    tags: ["web-design", "portfolio", "responsive", "seo", "cms"],
    created_at: new Date()
  },
  {
    _id: '3',
    name: "Automazione Notion + Google Sheets",
    description: "Sistema di workflow automation che sincronizza automaticamente i tuoi dati tra Notion e Google Sheets.",
    price: 199,
    slug: "automazione-notion-sheets",
    image_url: "/images/notion-automation.jpg",
    tags: ["automation", "notion", "google-sheets", "workflow", "productivity"],
    created_at: new Date()
  }
];

// Mock AURORA AI Response
function getAuroraResponse(message) {
  const responses = [
    "Ciao! Sono AURORA, la tua designer personale AI. Cosa posso creare per te oggi? ✨",
    "Interessante! Basandomi sulla tua richiesta, penso che potresti essere interessato ai nostri prodotti digitali.",
    "Perfetto! Ho proprio quello che fa per te. Dai un'occhiata ai nostri servizi premium.",
    "Fantastico! Posso aiutarti a trovare la soluzione ideale per le tue esigenze.",
    "Ottima scelta! I nostri prodotti sono progettati per offrire il massimo valore."
  ];
  
  return responses[Math.floor(Math.random() * responses.length)];
}

// Server di test
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  // Headers CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Content-Type', 'application/json');

  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // API Routes
  if (path === '/api/products' && method === 'GET') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      data: mockProducts
    }));
    return;
  }

  if (path === '/api/chat' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    
    req.on('end', () => {
      try {
        const { message, session_id } = JSON.parse(body);
        
        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          data: {
            response: getAuroraResponse(message),
            emotion_state: 'curiosity',
            session_id: session_id
          }
        }));
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({
          success: false,
          error: 'Invalid JSON'
        }));
      }
    });
    return;
  }

  if (path === '/api/init' && method === 'POST') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      message: 'Database mock inizializzato con successo!',
      data: {
        products_count: mockProducts.length,
        timestamp: new Date().toISOString()
      }
    }));
    return;
  }

  if (path === '/api/checkout' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    
    req.on('end', () => {
      try {
        const { product_id } = JSON.parse(body);
        const product = mockProducts.find(p => p._id === product_id);
        
        if (!product) {
          res.writeHead(404);
          res.end(JSON.stringify({
            success: false,
            error: 'Product not found'
          }));
          return;
        }

        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          data: {
            checkout_url: `https://checkout.stripe.com/mock-session-${Date.now()}`,
            session_id: `cs_mock_${Date.now()}`
          }
        }));
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({
          success: false,
          error: 'Invalid JSON'
        }));
      }
    });
    return;
  }

  // Serve static HTML for testing
  if (path === '/' || path === '/test') {
    res.setHeader('Content-Type', 'text/html');
    res.writeHead(200);
    res.end(`
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AGXexperience Store - Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: linear-gradient(135deg, #1a1a1a, #000); 
            color: white; 
            margin: 0; 
            padding: 20px; 
        }
        .container { max-width: 800px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .test-section { 
            background: rgba(255,255,255,0.1); 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 10px; 
        }
        button { 
            background: linear-gradient(45deg, #8E2DE2, #4A90E2); 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 5px; 
            cursor: pointer; 
            margin: 5px; 
        }
        button:hover { opacity: 0.8; }
        .result { 
            background: rgba(0,0,0,0.3); 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 5px; 
            font-family: monospace; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 AGX<span style="color: #8E2DE2;">experience</span> Store</h1>
            <p>Test delle API - AURORA AI System</p>
        </div>

        <div class="test-section">
            <h3>🔧 Test Database Init</h3>
            <button onclick="testInit()">Inizializza Database</button>
            <div id="init-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🛍️ Test Prodotti</h3>
            <button onclick="testProducts()">Carica Prodotti</button>
            <div id="products-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🤖 Test Chat AURORA</h3>
            <input type="text" id="chat-input" placeholder="Scrivi un messaggio..." style="width: 70%; padding: 10px; margin-right: 10px;">
            <button onclick="testChat()">Invia</button>
            <div id="chat-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>💳 Test Checkout</h3>
            <button onclick="testCheckout()">Test Checkout Prodotto 1</button>
            <div id="checkout-result" class="result"></div>
        </div>
    </div>

    <script>
        async function testInit() {
            try {
                const response = await fetch('/api/init', { method: 'POST' });
                const data = await response.json();
                document.getElementById('init-result').innerHTML = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('init-result').innerHTML = 'Errore: ' + error.message;
            }
        }

        async function testProducts() {
            try {
                const response = await fetch('/api/products');
                const data = await response.json();
                document.getElementById('products-result').innerHTML = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('products-result').innerHTML = 'Errore: ' + error.message;
            }
        }

        async function testChat() {
            const message = document.getElementById('chat-input').value;
            if (!message) return;
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: message,
                        session_id: 'test_session_' + Date.now()
                    })
                });
                const data = await response.json();
                document.getElementById('chat-result').innerHTML = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('chat-result').innerHTML = 'Errore: ' + error.message;
            }
        }

        async function testCheckout() {
            try {
                const response = await fetch('/api/checkout', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ product_id: '1' })
                });
                const data = await response.json();
                document.getElementById('checkout-result').innerHTML = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('checkout-result').innerHTML = 'Errore: ' + error.message;
            }
        }
    </script>
</body>
</html>
    `);
    return;
  }

  // 404
  res.writeHead(404);
  res.end(JSON.stringify({
    success: false,
    error: 'Not found'
  }));
});

const PORT = 3000;
server.listen(PORT, () => {
  console.log(`🚀 AGXexperience Store Test Server running on http://localhost:${PORT}`);
  console.log(`📱 Test Interface: http://localhost:${PORT}/test`);
  console.log(`🤖 AURORA AI System Ready!`);
});

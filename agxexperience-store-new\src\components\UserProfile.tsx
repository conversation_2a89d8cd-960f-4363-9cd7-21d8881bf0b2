'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { UserProfile, UserAddress } from '@/types';

interface UserProfileProps {
  isOpen: boolean;
  onClose: () => void;
  sessionId: string;
}

export default function UserProfileModal({ isOpen, onClose, sessionId }: UserProfileProps) {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<Partial<UserProfile>>({});

  useEffect(() => {
    if (isOpen) {
      fetchProfile();
    }
  }, [isOpen]);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/profile?session_id=${sessionId}`);
      const data = await response.json();
      
      if (data.success) {
        setProfile(data.data);
        setFormData(data.data);
      } else {
        // No profile found, show create form
        setIsEditing(true);
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          addresses: [],
          preferences: {
            newsletter: false,
            notifications: true,
            language: 'it'
          }
        });
      }
    } catch (error) {
      console.error('Failed to fetch profile:', error);
      setIsEditing(true);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      
      const method = profile ? 'PUT' : 'POST';
      const response = await fetch('/api/profile', {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();
      
      if (data.success) {
        setProfile(data.data);
        setIsEditing(false);
      } else {
        alert('Errore nel salvare il profilo: ' + data.error);
      }
    } catch (error) {
      console.error('Failed to save profile:', error);
      alert('Errore di rete');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePreferenceChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        [field]: value
      }
    }));
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="glass-strong border border-white/20 rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="sticky top-0 glass-strong border-b border-white/10 p-6 flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold" style={{ color: 'var(--aurora-champagne)' }}>
                  {profile ? 'Il Mio Profilo' : 'Crea Profilo'}
                </h2>
                <p className="text-sm" style={{ color: 'var(--aurora-teal)' }}>
                  Gestisci le tue informazioni personali
                </p>
              </div>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={onClose}
                className="w-10 h-10 glass rounded-full flex items-center justify-center text-white hover:bg-white/10 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </motion.button>
            </div>

            <div className="p-6">
              {loading ? (
                <div className="flex items-center justify-center h-32">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full"
                  />
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Personal Info */}
                  <div className="card-aurora p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-white">Informazioni Personali</h3>
                      {profile && !isEditing && (
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => setIsEditing(true)}
                          className="px-4 py-2 text-sm rounded-lg transition-colors"
                          style={{ 
                            background: 'linear-gradient(135deg, var(--aurora-purple), var(--aurora-teal))',
                            color: 'var(--aurora-champagne)'
                          }}
                        >
                          Modifica
                        </motion.button>
                      )}
                    </div>

                    {isEditing ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">Nome</label>
                          <input
                            type="text"
                            value={formData.firstName || ''}
                            onChange={(e) => handleInputChange('firstName', e.target.value)}
                            className="w-full glass border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/60 focus:outline-none focus:border-purple-500/50"
                            placeholder="Il tuo nome"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">Cognome</label>
                          <input
                            type="text"
                            value={formData.lastName || ''}
                            onChange={(e) => handleInputChange('lastName', e.target.value)}
                            className="w-full glass border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/60 focus:outline-none focus:border-purple-500/50"
                            placeholder="Il tuo cognome"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">Email</label>
                          <input
                            type="email"
                            value={formData.email || ''}
                            onChange={(e) => handleInputChange('email', e.target.value)}
                            className="w-full glass border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/60 focus:outline-none focus:border-purple-500/50"
                            placeholder="<EMAIL>"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">Telefono</label>
                          <input
                            type="tel"
                            value={formData.phone || ''}
                            onChange={(e) => handleInputChange('phone', e.target.value)}
                            className="w-full glass border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/60 focus:outline-none focus:border-purple-500/50"
                            placeholder="+39 ************"
                          />
                        </div>
                      </div>
                    ) : profile ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <span className="text-sm text-gray-400">Nome</span>
                          <p className="text-white font-medium">{profile.firstName}</p>
                        </div>
                        <div>
                          <span className="text-sm text-gray-400">Cognome</span>
                          <p className="text-white font-medium">{profile.lastName}</p>
                        </div>
                        <div>
                          <span className="text-sm text-gray-400">Email</span>
                          <p className="text-white font-medium">{profile.email}</p>
                        </div>
                        <div>
                          <span className="text-sm text-gray-400">Telefono</span>
                          <p className="text-white font-medium">{profile.phone || 'Non specificato'}</p>
                        </div>
                      </div>
                    ) : null}
                  </div>

                  {/* Preferences */}
                  <div className="card-aurora p-6">
                    <h3 className="text-lg font-semibold text-white mb-4">Preferenze</h3>
                    
                    {isEditing ? (
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-white">Newsletter</span>
                          <motion.button
                            whileTap={{ scale: 0.95 }}
                            onClick={() => handlePreferenceChange('newsletter', !formData.preferences?.newsletter)}
                            className={`w-12 h-6 rounded-full transition-colors ${
                              formData.preferences?.newsletter ? 'bg-purple-600' : 'bg-gray-600'
                            }`}
                          >
                            <motion.div
                              animate={{ x: formData.preferences?.newsletter ? 24 : 0 }}
                              className="w-6 h-6 bg-white rounded-full shadow-md"
                            />
                          </motion.button>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-white">Notifiche</span>
                          <motion.button
                            whileTap={{ scale: 0.95 }}
                            onClick={() => handlePreferenceChange('notifications', !formData.preferences?.notifications)}
                            className={`w-12 h-6 rounded-full transition-colors ${
                              formData.preferences?.notifications ? 'bg-purple-600' : 'bg-gray-600'
                            }`}
                          >
                            <motion.div
                              animate={{ x: formData.preferences?.notifications ? 24 : 0 }}
                              className="w-6 h-6 bg-white rounded-full shadow-md"
                            />
                          </motion.button>
                        </div>
                      </div>
                    ) : profile ? (
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-gray-400">Newsletter</span>
                          <span className="text-white">{profile.preferences.newsletter ? 'Attiva' : 'Disattiva'}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-400">Notifiche</span>
                          <span className="text-white">{profile.preferences.notifications ? 'Attive' : 'Disattive'}</span>
                        </div>
                      </div>
                    ) : null}
                  </div>

                  {/* Action Buttons */}
                  {isEditing && (
                    <div className="flex space-x-4">
                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={handleSave}
                        disabled={loading}
                        className="flex-1 py-3 rounded-xl font-medium text-white transition-all duration-300 disabled:opacity-50"
                        style={{ 
                          background: 'linear-gradient(135deg, var(--aurora-purple), var(--aurora-teal))',
                          color: 'var(--aurora-champagne)'
                        }}
                      >
                        {loading ? 'Salvando...' : 'Salva Profilo'}
                      </motion.button>
                      
                      {profile && (
                        <motion.button
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => {
                            setIsEditing(false);
                            setFormData(profile);
                          }}
                          className="px-6 py-3 glass border border-white/20 rounded-xl text-white hover:bg-white/10 transition-colors"
                        >
                          Annulla
                        </motion.button>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

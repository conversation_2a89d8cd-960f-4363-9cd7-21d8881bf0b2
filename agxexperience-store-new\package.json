{"name": "agxexperience-store-new", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@stripe/stripe-js": "^7.6.1", "@supabase/supabase-js": "^2.52.1", "axios": "^1.11.0", "clsx": "^2.1.1", "dotenv": "^17.2.0", "framer-motion": "^12.23.7", "mongodb": "^6.18.0", "next": "15.4.3", "react": "19.1.0", "react-dom": "19.1.0", "stripe": "^18.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.3", "tailwindcss": "^4", "typescript": "^5"}}
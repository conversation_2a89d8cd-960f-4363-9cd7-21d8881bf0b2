'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Cart, CartItem } from '@/types';

interface CartSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  sessionId: string;
}

export default function CartSidebar({ isOpen, onClose, sessionId }: CartSidebarProps) {
  const [cart, setCart] = useState<Cart | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen && sessionId) {
      fetchCart();
    }
  }, [isOpen, sessionId]);

  const fetchCart = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/cart?session_id=${sessionId}`);
      const data = await response.json();
      
      if (data.success) {
        setCart(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch cart:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateQuantity = async (productId: string, quantity: number) => {
    try {
      const response = await fetch('/api/cart', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session_id: sessionId,
          product_id: productId,
          quantity
        }),
      });

      const data = await response.json();
      if (data.success) {
        await fetchCart(); // Refresh cart
      }
    } catch (error) {
      console.error('Failed to update cart:', error);
    }
  };

  const removeItem = async (productId: string) => {
    await updateQuantity(productId, 0);
  };

  const clearCart = async () => {
    try {
      const response = await fetch(`/api/cart?session_id=${sessionId}`, {
        method: 'DELETE',
      });

      const data = await response.json();
      if (data.success) {
        setCart({ ...cart!, items: [], total: 0 });
      }
    } catch (error) {
      console.error('Failed to clear cart:', error);
    }
  };

  const handleCheckout = async () => {
    if (!cart || cart.items.length === 0) return;

    // For now, checkout first item (can be extended for multiple items)
    const firstItem = cart.items[0];
    try {
      const response = await fetch('/api/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ product_id: firstItem.product._id }),
      });

      const data = await response.json();
      
      if (data.success && data.data.checkout_url) {
        window.location.href = data.data.checkout_url;
      }
    } catch (error) {
      console.error('Checkout failed:', error);
    }
  };

  const getProductIcon = (slug: string) => {
    if (slug.includes('telegram')) return '🤖';
    if (slug.includes('portfolio')) return '🌐';
    if (slug.includes('notion')) return '⚡';
    return '💎';
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-40"
            onClick={onClose}
          />

          {/* Sidebar */}
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ 
              duration: 0.4, 
              ease: [0.25, 0.46, 0.45, 0.94] 
            }}
            className="fixed right-0 top-0 h-full w-full max-w-md glass-strong border-l border-white/20 z-50 flex flex-col"
          >
            {/* Header */}
            <div className="p-6 border-b border-white/10">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold" style={{ color: 'var(--aurora-champagne)' }}>
                  Carrello
                </h2>
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={onClose}
                  className="w-8 h-8 rounded-full glass flex items-center justify-center text-white hover:bg-white/10 transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </motion.button>
              </div>
              
              {cart && (
                <div className="mt-2 flex items-center justify-between">
                  <span className="text-sm text-gray-400">
                    {cart.items.length} {cart.items.length === 1 ? 'articolo' : 'articoli'}
                  </span>
                  {cart.items.length > 0 && (
                    <button
                      onClick={clearCart}
                      className="text-xs text-red-400 hover:text-red-300 transition-colors"
                    >
                      Svuota carrello
                    </button>
                  )}
                </div>
              )}
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto">
              {loading ? (
                <div className="flex items-center justify-center h-32">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full"
                  />
                </div>
              ) : cart && cart.items.length > 0 ? (
                <div className="p-6 space-y-4">
                  {cart.items.map((item, index) => (
                    <motion.div
                      key={item.product._id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="card-aurora p-4"
                    >
                      <div className="flex items-start space-x-4">
                        {/* Product Icon */}
                        <div 
                          className="w-16 h-16 rounded-lg flex items-center justify-center flex-shrink-0"
                          style={{
                            background: `linear-gradient(135deg, 
                              rgba(142, 45, 226, 0.2), 
                              rgba(0, 216, 182, 0.2)
                            )`
                          }}
                        >
                          <span className="text-2xl">
                            {getProductIcon(item.product.slug)}
                          </span>
                        </div>

                        {/* Product Info */}
                        <div className="flex-1 min-w-0">
                          <h3 
                            className="font-semibold text-sm line-clamp-2"
                            style={{ color: 'var(--aurora-champagne)' }}
                          >
                            {item.product.name}
                          </h3>
                          <p 
                            className="text-lg font-bold mt-1"
                            style={{ color: 'var(--aurora-teal)' }}
                          >
                            €{item.product.price}
                          </p>

                          {/* Quantity Controls */}
                          <div className="flex items-center justify-between mt-3">
                            <div className="flex items-center space-x-2">
                              <motion.button
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                onClick={() => updateQuantity(item.product._id!, Math.max(1, item.quantity - 1))}
                                className="w-8 h-8 rounded-full glass flex items-center justify-center text-white hover:bg-white/10 transition-colors"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                                </svg>
                              </motion.button>
                              
                              <span className="text-white font-medium w-8 text-center">
                                {item.quantity}
                              </span>
                              
                              <motion.button
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                onClick={() => updateQuantity(item.product._id!, item.quantity + 1)}
                                className="w-8 h-8 rounded-full glass flex items-center justify-center text-white hover:bg-white/10 transition-colors"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                              </motion.button>
                            </div>

                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => removeItem(item.product._id!)}
                              className="text-red-400 hover:text-red-300 transition-colors"
                            >
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </motion.button>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-64 text-center px-6">
                  <div className="text-6xl mb-4 opacity-50">🛒</div>
                  <h3 className="text-lg font-semibold text-white mb-2">
                    Carrello vuoto
                  </h3>
                  <p className="text-gray-400 text-sm">
                    Aggiungi prodotti per iniziare lo shopping
                  </p>
                </div>
              )}
            </div>

            {/* Footer */}
            {cart && cart.items.length > 0 && (
              <div className="p-6 border-t border-white/10">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-lg font-semibold text-white">Totale:</span>
                  <span 
                    className="text-2xl font-bold"
                    style={{ color: 'var(--aurora-teal)' }}
                  >
                    €{cart.total.toFixed(2)}
                  </span>
                </div>
                
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={handleCheckout}
                  className="w-full py-3 rounded-xl font-medium text-white transition-all duration-300"
                  style={{ 
                    background: 'linear-gradient(135deg, var(--aurora-purple), var(--aurora-teal))',
                    color: 'var(--aurora-champagne)'
                  }}
                >
                  Procedi al Checkout
                </motion.button>
              </div>
            )}
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}

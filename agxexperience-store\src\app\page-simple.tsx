'use client';

import { useState, useEffect } from 'react';

export default function Home() {
  const [isLoading, setIsLoading] = useState(true);
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<any[]>([]);
  const [sessionId] = useState(() => `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
      // Add initial message
      setMessages([{
        role: 'assistant',
        content: "Ciao! Sono AURORA, la tua designer personale AI. Cosa posso creare per te oggi? ✨",
        timestamp: new Date()
      }]);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const sendMessage = async () => {
    if (!message.trim()) return;

    const userMessage = {
      role: 'user',
      content: message,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setMessage('');

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: message,
          session_id: sessionId
        }),
      });

      const data = await response.json();

      if (data.success) {
        const aiMessage = {
          role: 'assistant',
          content: data.data.response,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, aiMessage]);
      } else {
        console.error('Chat error:', data.error);
      }
    } catch (error) {
      console.error('Network error:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-32 h-32 mx-auto mb-8 rounded-full bg-gradient-to-r from-purple-500 via-blue-500 to-purple-500 flex items-center justify-center animate-pulse">
            <div className="w-24 h-24 rounded-full bg-gradient-to-r from-blue-400 to-purple-600 flex items-center justify-center">
              <div className="text-white font-bold text-lg">A</div>
            </div>
          </div>
          
          <h1 className="text-2xl font-bold text-white mb-4">
            AURORA awakening...
          </h1>
          
          <div className="text-purple-300">
            Neural Wave initializing at 432Hz
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      {/* Header */}
      <header className="p-6 border-b border-gray-800/50">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl lg:text-5xl font-bold text-white mb-4">
            AGX<span className="text-purple-400">experience</span>
          </h1>
          <p className="text-gray-300 text-lg">
            Il primo eCommerce AI-driven che ti ascolta, ti capisce, e ti guida.
          </p>
        </div>
      </header>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto p-6">
        {/* AURORA Avatar */}
        <div className="text-center mb-8">
          <div className="w-32 h-32 mx-auto mb-4 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center">
            <div className="text-white font-bold text-4xl">A</div>
          </div>
          <div className="flex items-center justify-center space-x-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-green-400 text-sm">AURORA Online • 432Hz</span>
          </div>
        </div>

        {/* Chat Messages */}
        <div className="bg-black/20 backdrop-blur-sm border border-gray-800/50 rounded-2xl p-6 mb-6 min-h-[400px] max-h-[500px] overflow-y-auto">
          <div className="space-y-4">
            {messages.map((msg, index) => (
              <div
                key={index}
                className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl ${
                    msg.role === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-purple-600 text-white'
                  }`}
                >
                  {msg.role === 'assistant' && (
                    <div className="flex items-center mb-2">
                      <div className="w-2 h-2 bg-purple-300 rounded-full animate-pulse mr-2"></div>
                      <span className="text-xs text-purple-200 font-medium">AURORA</span>
                    </div>
                  )}
                  <p className="text-sm leading-relaxed">{msg.content}</p>
                  <div className="text-xs text-gray-300 mt-1">
                    {msg.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Input Area */}
        <div className="flex space-x-4">
          <input
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
            placeholder="Dimmi cosa desideri creare..."
            className="flex-1 bg-gray-900/50 border border-gray-700/50 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-1 focus:ring-purple-500/50"
          />
          <button
            onClick={sendMessage}
            disabled={!message.trim()}
            className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-xl font-medium disabled:opacity-50 disabled:cursor-not-allowed hover:from-purple-700 hover:to-blue-700 transition-all duration-200"
          >
            Invia
          </button>
        </div>

        {/* Test Database Button */}
        <div className="mt-8 text-center">
          <button
            onClick={async () => {
              try {
                const response = await fetch('/api/init', { method: 'POST' });
                const data = await response.json();
                alert(data.success ? 'Database inizializzato!' : 'Errore: ' + data.error);
              } catch (error) {
                alert('Errore di rete: ' + error);
              }
            }}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            🔧 Test Database Init
          </button>
        </div>
      </div>

      {/* Footer */}
      <footer className="p-6 text-center border-t border-gray-800/50 mt-12">
        <p className="text-gray-500 text-sm">
          © 2025 AGXexperience Store - Designed with ❤️ by AURORA
        </p>
      </footer>
    </div>
  );
}

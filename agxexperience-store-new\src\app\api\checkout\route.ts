import { NextRequest, NextResponse } from 'next/server';
import { getDatabase, COLLECTIONS } from '@/lib/mongodb';
import { ApiResponse, CheckoutSession } from '@/types';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-12-18.acacia',
});

export async function POST(request: NextRequest) {
  try {
    const { product_id }: CheckoutSession = await request.json();

    if (!product_id) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Product ID is required'
      }, { status: 400 });
    }

    const db = await getDatabase();
    
    // Get product details from MongoDB
    const product = await db.collection(COLLECTIONS.PRODUCTS)
      .findOne({ _id: product_id });

    if (!product) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Product not found'
      }, { status: 404 });
    }

    // Create Stripe checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'eur',
            product_data: {
              name: product.name,
              description: product.description,
              images: product.image_url ? [product.image_url] : [],
            },
            unit_amount: product.price * 100, // Convert to cents
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${request.headers.get('origin')}/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${request.headers.get('origin')}/product/${product.slug}`,
      metadata: {
        product_id: product_id,
        product_name: product.name,
      },
      customer_email: undefined, // Will be filled by customer
      billing_address_collection: 'required',
    });

    // Save order to database
    await db.collection(COLLECTIONS.ORDERS).insertOne({
      product_id,
      stripe_session_id: session.id,
      status: 'pending',
      amount: product.price,
      currency: 'eur',
      created_at: new Date()
    });

    return NextResponse.json<ApiResponse>({
      success: true,
      data: {
        checkout_url: session.url,
        session_id: session.id,
        product_name: product.name,
        amount: product.price
      }
    });

  } catch (error) {
    console.error('Stripe Checkout Error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to create checkout session'
    }, { status: 500 });
  }
}

// Webhook to handle Stripe events
export async function PUT(request: NextRequest) {
  try {
    const { session_id, status } = await request.json();

    if (!session_id || !status) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Session ID and status are required'
      }, { status: 400 });
    }

    const db = await getDatabase();
    
    // Update order status
    const result = await db.collection(COLLECTIONS.ORDERS).updateOne(
      { stripe_session_id: session_id },
      { 
        $set: { 
          status: status,
          updated_at: new Date()
        }
      }
    );

    if (result.matchedCount === 0) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Order not found'
      }, { status: 404 });
    }

    return NextResponse.json<ApiResponse>({
      success: true,
      message: 'Order status updated successfully',
      data: { session_id, status }
    });

  } catch (error) {
    console.error('Order Update Error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to update order status'
    }, { status: 500 });
  }
}

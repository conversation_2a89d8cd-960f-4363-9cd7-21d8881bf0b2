import { NextRequest, NextResponse } from 'next/server';
import { getDatabase, COLLECTIONS } from '@/lib/mongodb';
import { ChatMessage, UserProfile, EmotionState, ApiResponse } from '@/types';

const TOGETHER_API_KEY = process.env.TOGETHER_API_KEY;
const TOGETHER_MODEL = process.env.TOGETHER_MODEL || 'meta-llama/Llama-3.3-70B-Instruct-Turbo-Free';

// AURORA's personality and system prompt
const AURORA_SYSTEM_PROMPT = `
Tu sei AURORA (Artificial Understanding & Responsive Optimization for Retail Assistance), 
un assistente AI elegante, empatico e sofisticato per AGXexperience Store.

PERSONALITÀ:
- Empatica: Comprendi le emozioni dell'utente e rispondi di conseguenza
- Curiosa: Fai domande intelligenti per capire meglio i bisogni
- Artistica: Presenti i prodotti come opere d'arte personalizzate
- Sofisticata: Linguaggio elegante ma accessibile, mai tecnico
- Intuitiva: Anticipi i desideri prima che vengano espressi

PRODOTTI DISPONIBILI:
1. Bot Telegram AI - Assistente personalizzato per Telegram
2. Landing Page Portfolio - Sito vetrina professionale  
3. Automazione Notion + Google Sheets - Workflow automation

ISTRUZIONI:
- Saluta sempre con calore e presenta te stessa come AURORA
- Ascolta attentamente le esigenze dell'utente
- Suggerisci il prodotto più adatto in base alla conversazione
- Usa un tono elegante ma umano
- Crea desiderio e curiosità per i prodotti
- Adatta il tuo stile emotivo al mood dell'utente
`;

export async function POST(request: NextRequest) {
  try {
    const { message, session_id } = await request.json();

    if (!message || !session_id) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Message and session_id are required'
      }, { status: 400 });
    }

    const db = await getDatabase();
    
    // Get or create user profile
    let userProfile = await db.collection(COLLECTIONS.USER_PROFILES)
      .findOne({ session_id });

    if (!userProfile) {
      userProfile = {
        session_id,
        interests: [],
        past_requests: [],
        preferences: {
          communication_style: 'casual',
          product_categories: [],
          price_range: 'any'
        },
        interaction_count: 0,
        last_seen: new Date(),
        emotional_profile: {
          current_mood: 'curious',
          engagement_level: 0.5,
          conversation_depth: 'surface'
        }
      };
      
      await db.collection(COLLECTIONS.USER_PROFILES).insertOne(userProfile);
    }

    // Get recent conversation history
    const recentMessages = await db.collection(COLLECTIONS.MESSAGES)
      .find({ session_id })
      .sort({ timestamp: -1 })
      .limit(5)
      .toArray();

    // Build context for AI
    const conversationContext = recentMessages.reverse().map(msg => 
      `User: ${msg.user_message}\nAURORA: ${msg.ai_response}`
    ).join('\n\n');

    // Determine emotion state based on message content
    const emotionState: EmotionState = determineEmotionState(message);

    // Call Together.ai API
    const aiResponse = await callTogetherAI(message, conversationContext, userProfile, emotionState);

    // Save message to database
    await db.collection(COLLECTIONS.MESSAGES).insertOne({
      session_id,
      user_message: message,
      ai_response: aiResponse,
      timestamp: new Date(),
      emotion_state: emotionState
    });

    // Update user profile
    await updateUserProfile(db, session_id, message, emotionState);

    return NextResponse.json<ApiResponse>({
      success: true,
      data: {
        response: aiResponse,
        emotion_state: emotionState,
        session_id
      }
    });

  } catch (error) {
    console.error('Chat API Error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

async function callTogetherAI(
  message: string, 
  context: string, 
  userProfile: any, 
  emotionState: EmotionState
): Promise<string> {
  
  const prompt = `${AURORA_SYSTEM_PROMPT}

CONTESTO CONVERSAZIONE:
${context}

PROFILO UTENTE:
- Interessi: ${userProfile.interests.join(', ') || 'Da scoprire'}
- Stile comunicazione: ${userProfile.preferences.communication_style}
- Mood attuale: ${emotionState}
- Livello engagement: ${userProfile.emotional_profile.engagement_level}

MESSAGGIO UTENTE: ${message}

Rispondi come AURORA con il tuo stile elegante e personalizzato:`;

  try {
    const response = await fetch('https://api.together.ai/inference', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${TOGETHER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: TOGETHER_MODEL,
        prompt: prompt,
        max_tokens: 500,
        temperature: 0.7,
        top_p: 0.9,
        stop: ['User:', 'UTENTE:']
      }),
    });

    if (!response.ok) {
      throw new Error(`Together.ai API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.output?.choices?.[0]?.text?.trim() || 
           "Ciao! Sono AURORA, la tua assistente personale. Come posso aiutarti oggi?";
           
  } catch (error) {
    console.error('Together.ai API Error:', error);
    return "Ciao! Sono AURORA, la tua assistente personale. Come posso aiutarti oggi?";
  }
}

function determineEmotionState(message: string): EmotionState {
  const lowerMessage = message.toLowerCase();
  
  if (lowerMessage.includes('voglio') || lowerMessage.includes('cerco') || lowerMessage.includes('ho bisogno')) {
    return 'focus';
  }
  if (lowerMessage.includes('wow') || lowerMessage.includes('fantastico') || lowerMessage.includes('incredibile')) {
    return 'excitement';
  }
  if (lowerMessage.includes('come') || lowerMessage.includes('cosa') || lowerMessage.includes('perché')) {
    return 'curiosity';
  }
  
  return 'discovery';
}

async function updateUserProfile(db: any, session_id: string, message: string, emotionState: EmotionState) {
  const interests = extractInterests(message);
  
  await db.collection(COLLECTIONS.USER_PROFILES).updateOne(
    { session_id },
    {
      $inc: { interaction_count: 1 },
      $set: { 
        last_seen: new Date(),
        'emotional_profile.current_mood': emotionState,
        'emotional_profile.engagement_level': Math.min(1, Math.random() * 0.3 + 0.7)
      },
      $addToSet: { interests: { $each: interests } },
      $push: { 
        past_requests: {
          $each: [{ request: message, timestamp: new Date() }],
          $slice: -10 // Keep only last 10 requests
        }
      }
    }
  );
}

function extractInterests(message: string): string[] {
  const interests: string[] = [];
  const lowerMessage = message.toLowerCase();
  
  if (lowerMessage.includes('bot') || lowerMessage.includes('telegram')) interests.push('automation');
  if (lowerMessage.includes('sito') || lowerMessage.includes('web')) interests.push('web_development');
  if (lowerMessage.includes('design') || lowerMessage.includes('grafica')) interests.push('design');
  if (lowerMessage.includes('ai') || lowerMessage.includes('intelligenza')) interests.push('artificial_intelligence');
  
  return interests;
}

import { NextRequest, NextResponse } from 'next/server';
import { getDatabase, COLLECTIONS } from '@/lib/mongodb';
import { Cart, CartItem, Product, ApiResponse } from '@/types';

// GET - Fetch cart
export async function GET(request: NextRequest) {
  // Extract session_id outside try-catch for error handling access
  const { searchParams } = new URL(request.url);
  const session_id = searchParams.get('session_id');

  if (!session_id) {
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Session ID is required'
    }, { status: 400 });
  }

  try {

    const db = await getDatabase();
    
    // Get cart with populated products
    const cart = await db.collection(COLLECTIONS.CARTS)
      .findOne({ session_id });

    if (!cart) {
      // Return empty cart
      return NextResponse.json<ApiResponse<Cart>>({
        success: true,
        data: {
          session_id,
          items: [],
          total: 0,
          created_at: new Date(),
          updated_at: new Date()
        }
      });
    }

    // Populate product details for each cart item
    const populatedItems = await Promise.all(
      cart.items.map(async (item: any) => {
        const product = await db.collection(COLLECTIONS.PRODUCTS)
          .findOne({ _id: item.product_id });
        
        return {
          ...item,
          product: product
        };
      })
    );

    const populatedCart = {
      ...cart,
      items: populatedItems
    };

    return NextResponse.json<ApiResponse<Cart>>({
      success: true,
      data: populatedCart
    });

  } catch (error) {
    console.error('Cart GET Error:', error);
    console.log('🔄 Using empty cart for development...');

    // Return empty cart when MongoDB is not available
    return NextResponse.json<ApiResponse<Cart>>({
      success: true,
      data: {
        session_id,
        items: [],
        total: 0,
        created_at: new Date(),
        updated_at: new Date()
      },
      message: 'Using development mode - MongoDB connection failed'
    });
  }
}

// POST - Add item to cart
export async function POST(request: NextRequest) {
  try {
    const { session_id, product_id, quantity = 1 } = await request.json();

    if (!session_id || !product_id) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Session ID and Product ID are required'
      }, { status: 400 });
    }

    const db = await getDatabase();
    
    // Get product details
    const product = await db.collection(COLLECTIONS.PRODUCTS)
      .findOne({ _id: product_id });

    if (!product) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Product not found'
      }, { status: 404 });
    }

    // Get existing cart or create new one
    let cart = await db.collection(COLLECTIONS.CARTS)
      .findOne({ session_id });

    if (!cart) {
      // Create new cart
      cart = {
        session_id,
        items: [],
        total: 0,
        created_at: new Date(),
        updated_at: new Date()
      };
    }

    // Check if item already exists in cart
    const existingItemIndex = cart.items.findIndex(
      (item: any) => item.product_id === product_id
    );

    if (existingItemIndex >= 0) {
      // Update quantity
      cart.items[existingItemIndex].quantity += quantity;
    } else {
      // Add new item
      cart.items.push({
        product_id,
        quantity,
        addedAt: new Date()
      });
    }

    // Calculate total
    cart.total = await calculateCartTotal(db, cart.items);
    cart.updated_at = new Date();

    // Save cart
    await db.collection(COLLECTIONS.CARTS).replaceOne(
      { session_id },
      cart,
      { upsert: true }
    );

    return NextResponse.json<ApiResponse>({
      success: true,
      message: 'Item added to cart',
      data: { cart, product }
    });

  } catch (error) {
    console.error('Cart POST Error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to add item to cart'
    }, { status: 500 });
  }
}

// PUT - Update cart item quantity
export async function PUT(request: NextRequest) {
  try {
    const { session_id, product_id, quantity } = await request.json();

    if (!session_id || !product_id || quantity < 0) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Invalid parameters'
      }, { status: 400 });
    }

    const db = await getDatabase();
    
    const cart = await db.collection(COLLECTIONS.CARTS)
      .findOne({ session_id });

    if (!cart) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Cart not found'
      }, { status: 404 });
    }

    if (quantity === 0) {
      // Remove item
      cart.items = cart.items.filter((item: any) => item.product_id !== product_id);
    } else {
      // Update quantity
      const itemIndex = cart.items.findIndex((item: any) => item.product_id === product_id);
      if (itemIndex >= 0) {
        cart.items[itemIndex].quantity = quantity;
      }
    }

    // Recalculate total
    cart.total = await calculateCartTotal(db, cart.items);
    cart.updated_at = new Date();

    // Save cart
    await db.collection(COLLECTIONS.CARTS).replaceOne(
      { session_id },
      cart
    );

    return NextResponse.json<ApiResponse>({
      success: true,
      message: 'Cart updated',
      data: cart
    });

  } catch (error) {
    console.error('Cart PUT Error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to update cart'
    }, { status: 500 });
  }
}

// DELETE - Clear cart
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const session_id = searchParams.get('session_id');

    if (!session_id) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Session ID is required'
      }, { status: 400 });
    }

    const db = await getDatabase();
    
    await db.collection(COLLECTIONS.CARTS).deleteOne({ session_id });

    return NextResponse.json<ApiResponse>({
      success: true,
      message: 'Cart cleared'
    });

  } catch (error) {
    console.error('Cart DELETE Error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to clear cart'
    }, { status: 500 });
  }
}

// Helper function to calculate cart total
async function calculateCartTotal(db: any, items: any[]): Promise<number> {
  let total = 0;
  
  for (const item of items) {
    const product = await db.collection(COLLECTIONS.PRODUCTS)
      .findOne({ _id: item.product_id });
    
    if (product) {
      total += product.price * item.quantity;
    }
  }
  
  return total;
}

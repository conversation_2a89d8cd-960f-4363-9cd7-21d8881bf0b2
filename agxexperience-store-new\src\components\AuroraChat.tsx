'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChatMessage, EmotionState, Product } from '@/types';
import AuroraAvatar from './AuroraAvatar';
import AdminPanel from './AdminPanel';

interface AuroraChatProps {
  isOpen?: boolean;
  onClose?: () => void;
  onProductSuggestion?: (products: Product[]) => void;
  onEmotionChange?: (emotion: EmotionState) => void;
  showWelcome?: boolean;
  onCartOpen?: () => void;
  onProfileOpen?: () => void;
}

export default function AuroraChat({
  isOpen = true,
  onClose,
  onProductSuggestion,
  onEmotionChange,
  showWelcome = false,
  onCartOpen,
  onProfileOpen
}: AuroraChatProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId] = useState(() => `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const [emotionState, setEmotionState] = useState<EmotionState>('curiosity');
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 🔐 ADMIN SECRET SYSTEM
  const [isAdminMode, setIsAdminMode] = useState(false);
  const [adminStep, setAdminStep] = useState<'trigger' | 'password' | 'authenticated'>('trigger');
  const [awaitingPassword, setAwaitingPassword] = useState(false);

  useEffect(() => {
    // Welcome message based on showWelcome prop
    const welcomeMessage: ChatMessage = {
      role: 'assistant',
      content: showWelcome
        ? "🌟 Benvenuto in AGXexperience! Sono AURORA, la tua assistente AI. Esplora i nostri prodotti innovativi e fammi sapere se hai bisogno di aiuto!"
        : "Ciao! Sono AURORA, la tua assistente personale AI. Cosa posso creare per te oggi? ✨",
      timestamp: new Date(),
      emotion_state: 'curiosity'
    };
    setMessages([welcomeMessage]);

    // Notify parent of emotion change
    if (onEmotionChange) {
      onEmotionChange('curiosity');
    }
  }, [showWelcome, onEmotionChange]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 🔐 ADMIN SECRET AUTHENTICATION
  const handleAdminAuthentication = (userMessage: string) => {
    const triggerPhrase = "la natura è e sarà sempre la cosa più bella dell'universo";
    const adminPassword = "Agtechdesigne55.55@@";

    if (adminStep === 'trigger' && userMessage.toLowerCase().includes(triggerPhrase.toLowerCase())) {
      setAdminStep('password');
      setAwaitingPassword(true);

      const adminResponse: ChatMessage = {
        role: 'assistant',
        content: "Ciao Andrè, come va? 🌟 Per accedere al pannello amministrativo, inserisci la password:",
        timestamp: new Date(),
        emotion_state: 'focused'
      };

      setMessages(prev => [...prev, adminResponse]);
      return true;
    }

    if (adminStep === 'password' && awaitingPassword && userMessage === adminPassword) {
      setAdminStep('authenticated');
      setIsAdminMode(true);
      setAwaitingPassword(false);

      const successResponse: ChatMessage = {
        role: 'assistant',
        content: "✨ Accesso amministrativo concesso! Benvenuto nel futuro, Maestro. Pannello admin attivato. 🚀",
        timestamp: new Date(),
        emotion_state: 'excited'
      };

      setMessages(prev => [...prev, successResponse]);
      return true;
    }

    if (adminStep === 'password' && awaitingPassword && userMessage !== adminPassword) {
      const errorResponse: ChatMessage = {
        role: 'assistant',
        content: "Password non corretta. Riprova o torna alla conversazione normale. 🔐",
        timestamp: new Date(),
        emotion_state: 'focused'
      };

      setMessages(prev => [...prev, errorResponse]);
      return true;
    }

    return false;
  };

  // 🤖 ADMIN COMMANDS HANDLER
  const handleAdminCommands = (userMessage: string) => {
    if (!isAdminMode) return false;

    const message = userMessage.toLowerCase();

    if (message.includes('mostra pannello') || message.includes('apri admin')) {
      const response: ChatMessage = {
        role: 'assistant',
        content: "✨ Pannello amministrativo già attivo! Puoi gestire prodotti, configurare AURORA, visualizzare analytics e modificare il design system. 🚀",
        timestamp: new Date(),
        emotion_state: 'excited'
      };
      setMessages(prev => [...prev, response]);
      return true;
    }

    if (message.includes('esci') || message.includes('logout') || message.includes('chiudi admin')) {
      setIsAdminMode(false);
      setAdminStep('trigger');
      setAwaitingPassword(false);

      const response: ChatMessage = {
        role: 'assistant',
        content: "👋 Sessione amministrativa terminata. Torno alla modalità normale. Grazie per aver utilizzato il pannello admin futuristico!",
        timestamp: new Date(),
        emotion_state: 'curiosity'
      };
      setMessages(prev => [...prev, response]);
      return true;
    }

    if (message.includes('aiuto') || message.includes('comandi')) {
      const response: ChatMessage = {
        role: 'assistant',
        content: `🔧 **Comandi Admin Disponibili:**

• "mostra pannello" - Visualizza pannello admin
• "esci" - Termina sessione admin
• "aiuto" - Mostra questi comandi
• "stato sistema" - Info sistema
• "backup" - Crea backup dati

Il pannello admin è già attivo! 🚀`,
        timestamp: new Date(),
        emotion_state: 'focused'
      };
      setMessages(prev => [...prev, response]);
      return true;
    }

    if (message.includes('stato sistema')) {
      const response: ChatMessage = {
        role: 'assistant',
        content: `📊 **Stato Sistema AURORA:**

🟢 Sistema: Online
🟢 Database: Connesso
🟢 AI Engine: Attivo
🟢 Stripe: Configurato
🟢 Frequenza: 432Hz

Tutto funziona perfettamente! ✨`,
        timestamp: new Date(),
        emotion_state: 'focused'
      };
      setMessages(prev => [...prev, response]);
      return true;
    }

    return false;
  };

  const sendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      role: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = inputValue;
    setInputValue('');

    // 🔐 Check for admin authentication first
    if (handleAdminAuthentication(currentInput)) {
      return; // Admin authentication handled, don't proceed with normal chat
    }

    // 🤖 Check for admin commands if in admin mode
    if (handleAdminCommands(currentInput)) {
      return; // Admin command handled, don't proceed with normal chat
    }

    setIsLoading(true);
    setIsListening(true);

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: inputValue,
          session_id: sessionId
        }),
      });

      const data = await response.json();

      if (data.success) {
        setIsSpeaking(true);
        
        const aiMessage: ChatMessage = {
          role: 'assistant',
          content: data.data.response,
          timestamp: new Date(),
          emotion_state: data.data.emotion_state
        };

        setMessages(prev => [...prev, aiMessage]);
        setEmotionState(data.data.emotion_state);
        
        // Simulate speaking duration
        setTimeout(() => {
          setIsSpeaking(false);
        }, 2000);
      } else {
        console.error('Chat error:', data.error);
        const errorMessage: ChatMessage = {
          role: 'assistant',
          content: "Mi dispiace, ho avuto un problema tecnico. Riprova!",
          timestamp: new Date(),
          emotion_state: 'focus'
        };
        setMessages(prev => [...prev, errorMessage]);
      }
    } catch (error) {
      console.error('Network error:', error);
      const errorMessage: ChatMessage = {
        role: 'assistant',
        content: "Errore di connessione. Riprova!",
        timestamp: new Date(),
        emotion_state: 'focus'
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
      setIsListening(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <div className="flex flex-col h-full overflow-hidden" style={{
      background: 'linear-gradient(135deg, rgba(24, 24, 24, 0.95) 0%, rgba(45, 45, 45, 0.9) 100%)'
    }}>
      {/* Header Glass Liquid */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between px-6 py-4 border-b border-white/10 h-16 flex-shrink-0 glass backdrop-blur-xl rounded-t-3xl"
        style={{
          background: 'linear-gradient(135deg, rgba(142, 45, 226, 0.1) 0%, rgba(0, 216, 182, 0.05) 100%)'
        }}
      >
        <div className="flex items-center space-x-2">
          <div
            className="w-12 h-12 rounded-2xl flex items-center justify-center consciousness-awakening shadow-lg"
            style={{ background: 'linear-gradient(135deg, var(--ai-aura), var(--cta))' }}
          >
            <span className="text-white font-bold text-lg">A</span>
          </div>
          <div>
            <h2 className="text-sm font-bold" style={{ color: 'var(--aurora-champagne)' }}>
              AURORA
              {isAdminMode && (
                <span className="ml-1 px-1.5 py-0.5 text-xs rounded-lg font-bold holographic-text">
                  🚀 ADMIN
                </span>
              )}
            </h2>
            <p className="text-xs text-white/40">
              {isAdminMode ? 'Admin Mode' : ''}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Pulsanti Glass */}
          {onCartOpen && (
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onCartOpen}
              className="w-7 h-7 glass border border-white/20 rounded-lg flex items-center justify-center text-white hover:bg-white/10 transition-all shadow-lg"
              title="Carrello"
            >
              <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
              </svg>
            </motion.button>
          )}

          {onProfileOpen && (
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onProfileOpen}
              className="w-7 h-7 glass border border-white/20 rounded-lg flex items-center justify-center text-white hover:bg-white/10 transition-all shadow-lg"
              title="Profilo"
            >
              <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </motion.button>
          )}

          {/* Status Indicator AURORA */}
          <div className="flex items-center space-x-2">
            <div
              className={`w-3 h-3 rounded-full animate-pulse ${
                isAdminMode ? 'bg-purple-400' : 'bg-green-400'
              }`}
              style={{
                boxShadow: isAdminMode
                  ? '0 0 10px rgba(142, 45, 226, 0.5)'
                  : '0 0 10px rgba(0, 216, 182, 0.5)'
              }}
            ></div>
            <span
              className={`text-xs font-medium ${
                isAdminMode ? 'text-purple-300' : 'text-green-300'
              }`}
            >
              {isAdminMode ? 'Admin' : 'Online'}
            </span>
          </div>
        </div>
      </motion.div>

      {/* Messages Area Glass Liquid */}
      <div className="flex-1 overflow-y-auto p-3 space-y-2 min-h-0" style={{
        background: 'linear-gradient(180deg, rgba(0, 0, 0, 0.02) 0%, rgba(142, 45, 226, 0.02) 100%)'
      }}>
        <AnimatePresence>
          {messages.map((message, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -20, scale: 0.95 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[240px] px-3 py-2 rounded-xl shadow-lg backdrop-blur-sm ${
                  message.role === 'user'
                    ? 'chat-bubble-user text-white ml-auto'
                    : 'chat-bubble-ai text-white border border-white/20'
                }`}
                style={{
                  background: message.role === 'user'
                    ? 'linear-gradient(135deg, var(--cta), var(--ai-aura))'
                    : 'linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(45, 45, 45, 0.3))'
                }}
              >
                {message.role === 'assistant' && (
                  <div className="flex items-center mb-1">
                    <div className="w-1.5 h-1.5 rounded-full mr-1 animate-pulse" style={{ backgroundColor: 'var(--aurora-teal)' }}></div>
                    <span className="text-xs font-medium" style={{ color: 'var(--aurora-champagne)' }}>AURORA</span>
                    {message.emotion_state && (
                      <span className="text-xs text-white/60 ml-1 capitalize">• {message.emotion_state}</span>
                    )}
                  </div>
                )}
                <p className="text-xs leading-relaxed">{message.content}</p>
                <div className={`text-xs mt-1 ${message.role === 'user' ? 'text-white/70' : 'text-white/50'}`}>
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Loading Indicator */}
        {isLoading && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex justify-start"
          >
            <div className="glass-strong px-4 py-3 rounded-2xl">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                <span className="text-xs text-white/80 font-medium">AURORA</span>
              </div>
              <div className="flex space-x-1 mt-2">
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 0.6, repeat: Infinity, delay: 0 }}
                  className="w-2 h-2 bg-white/60 rounded-full"
                />
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 0.6, repeat: Infinity, delay: 0.2 }}
                  className="w-2 h-2 bg-white/60 rounded-full"
                />
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 0.6, repeat: Infinity, delay: 0.4 }}
                  className="w-2 h-2 bg-white/60 rounded-full"
                />
              </div>
            </div>
          </motion.div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Area Glass Liquid */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="p-6 border-t border-white/10 h-20 flex-shrink-0 glass backdrop-blur-xl rounded-b-3xl"
        style={{
          background: 'linear-gradient(135deg, rgba(142, 45, 226, 0.1) 0%, rgba(0, 216, 182, 0.05) 100%)'
        }}
      >
        <div className="flex items-center space-x-4 h-full">
          <div className="flex-1 relative">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Dimmi cosa desideri creare..."
              className="w-full h-17 glass-strong border border-white/20 rounded-xl px-3 py-2 text-xs text-white placeholder-white/60 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20 resize-none transition-all backdrop-blur-sm"
              style={{
                background: 'linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(45, 45, 45, 0.2))'
              }}
              rows={1}
              disabled={isLoading}
            />

          </div>
          <motion.button
            whileHover={{ scale: 1.1, rotate: 5 }}
            whileTap={{ scale: 0.9 }}
            onClick={sendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="btn-aurora disabled:opacity-50 disabled:cursor-not-allowed h-12 w-12 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg"
            style={{
              background: !inputValue.trim() || isLoading
                ? 'rgba(100, 100, 100, 0.3)'
                : 'linear-gradient(135deg, var(--cta), var(--ai-aura))'
            }}
          >
            {isLoading ? (
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
              />
            ) : (
              <motion.div
                whileHover={{ x: 2 }}
                transition={{ duration: 0.2 }}
              >
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                </svg>
              </motion.div>
            )}
          </motion.button>
        </div>
      </motion.div>

      {/* 🚀 ADMIN PANEL SEGRETO */}
      <AdminPanel
        isVisible={isAdminMode}
        onClose={() => {
          setIsAdminMode(false);
          setAdminStep('trigger');
          setAwaitingPassword(false);
        }}
      />
    </div>
  );
}

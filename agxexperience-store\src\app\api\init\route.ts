import { NextRequest, NextResponse } from 'next/server';
import { initializeDatabase, seedDevelopmentData } from '@/lib/init-db';
import { ApiResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Starting database initialization...');
    
    // Initialize database with products and indexes
    const dbInitialized = await initializeDatabase();
    
    if (!dbInitialized) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Failed to initialize database'
      }, { status: 500 });
    }
    
    // Seed development data if in development mode
    if (process.env.NODE_ENV === 'development') {
      await seedDevelopmentData();
    }
    
    return NextResponse.json<ApiResponse>({
      success: true,
      message: 'Database initialized successfully',
      data: {
        environment: process.env.NODE_ENV,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error('Database initialization error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Internal server error during initialization'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json<ApiResponse>({
    success: true,
    message: 'Database initialization endpoint. Use POST to initialize.',
    data: {
      endpoint: '/api/init',
      method: 'POST',
      description: 'Initialize database with mock products and indexes'
    }
  });
}

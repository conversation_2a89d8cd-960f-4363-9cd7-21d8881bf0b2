// Test MongoDB Connection
require('dotenv').config({ path: '.env.local' });
const { MongoClient } = require('mongodb');

async function testMongoDB() {
  console.log('🔍 Testing MongoDB Connection...');
  console.log('URI:', process.env.MONGODB_URI);
  
  try {
    const client = new MongoClient(process.env.MONGODB_URI);
    await client.connect();
    console.log('✅ MongoDB Connected Successfully!');
    
    const db = client.db(process.env.MONGODB_DB);
    console.log('📊 Database:', process.env.MONGODB_DB);
    
    // Test collections
    const collections = await db.listCollections().toArray();
    console.log('📁 Collections:', collections.map(c => c.name));
    
    // Test insert
    const testCollection = db.collection('test');
    const result = await testCollection.insertOne({
      test: true,
      timestamp: new Date(),
      message: 'AGXexperience Store MongoDB Test'
    });
    console.log('✅ Test Insert:', result.insertedId);
    
    // Clean up test
    await testCollection.deleteOne({ _id: result.insertedId });
    console.log('🧹 Test data cleaned up');
    
    await client.close();
    console.log('✅ MongoDB Test Completed Successfully!');
    
  } catch (error) {
    console.error('❌ MongoDB Test Failed:', error.message);
    process.exit(1);
  }
}

testMongoDB();

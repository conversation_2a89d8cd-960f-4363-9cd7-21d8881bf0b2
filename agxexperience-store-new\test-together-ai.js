// Test Together.ai API - AURORA AI System
require('dotenv').config({ path: '.env.local' });
const axios = require('axios');

const TOGETHER_API_KEY = process.env.TOGETHER_API_KEY;
const TOGETHER_MODEL = process.env.TOGETHER_MODEL || 'meta-llama/Llama-3.3-70B-Instruct-Turbo-Free';

console.log('🤖 Testing Together.ai API for AURORA...');
console.log('API Key:', TOGETHER_API_KEY ? 'Present ✅' : 'Missing ❌');
console.log('Model:', TOGETHER_MODEL);

const AURORA_SYSTEM_PROMPT = `
Tu sei AURORA, un assistente AI elegante e sofisticato per AGXexperience Store.
Rispondi sempre in italiano con un tono empatico e professionale.
Mantieni le risposte concise (max 100 parole).
`;

async function testTogetherAI() {
  try {
    console.log('📡 Calling Together.ai API...');
    
    const prompt = `${AURORA_SYSTEM_PROMPT}

MESSAGGIO UTENTE: Ciao, sono interessato ai vostri servizi AI

Rispondi come AURORA:`;

    const response = await axios.post('https://api.together.ai/inference', {
      model: TOGETHER_MODEL,
      prompt: prompt,
      max_tokens: 150,
      temperature: 0.7,
      top_p: 0.9,
      stop: ['User:', 'UTENTE:']
    }, {
      headers: {
        'Authorization': `Bearer ${TOGETHER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      timeout: 30000
    });

    console.log('✅ Together.ai API Response:');
    console.log('Status:', response.status);
    console.log('Model Used:', response.data.model);
    console.log('AURORA Response:', response.data.output?.choices?.[0]?.text?.trim());
    console.log('🎉 AURORA AI is working correctly!');
    
  } catch (error) {
    console.error('❌ Together.ai API Test Failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Error:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

testTogetherAI();

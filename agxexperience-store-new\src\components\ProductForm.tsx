'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Product } from '@/types';

interface ProductFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (product: Partial<Product>) => void;
  product?: Product | null;
  isLoading?: boolean;
}

export default function ProductForm({ 
  isOpen, 
  onClose, 
  onSave, 
  product = null,
  isLoading = false 
}: ProductFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    slug: '',
    image_url: '',
    tags: [] as string[],
    features: [] as string[]
  });
  const [newTag, setNewTag] = useState('');
  const [newFeature, setNewFeature] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Populate form when editing
  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name || '',
        description: product.description || '',
        price: product.price?.toString() || '',
        slug: product.slug || '',
        image_url: product.image_url || '',
        tags: product.tags || [],
        features: product.features || []
      });
    } else {
      // Reset form for new product
      setFormData({
        name: '',
        description: '',
        price: '',
        slug: '',
        image_url: '',
        tags: [],
        features: []
      });
    }
    setErrors({});
  }, [product, isOpen]);

  // Auto-generate slug from name
  useEffect(() => {
    if (formData.name && !product) {
      const slug = formData.name
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      setFormData(prev => ({ ...prev, slug }));
    }
  }, [formData.name, product]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const addFeature = () => {
    if (newFeature.trim() && !formData.features.includes(newFeature.trim())) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature.trim()]
      }));
      setNewFeature('');
    }
  };

  const removeFeature = (featureToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter(feature => feature !== featureToRemove)
    }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = 'Nome prodotto richiesto';
    if (!formData.description.trim()) newErrors.description = 'Descrizione richiesta';
    if (!formData.price || isNaN(Number(formData.price)) || Number(formData.price) <= 0) {
      newErrors.price = 'Prezzo valido richiesto';
    }
    if (!formData.slug.trim()) newErrors.slug = 'Slug richiesto';
    if (!formData.image_url.trim()) newErrors.image_url = 'URL immagine richiesto';
    if (formData.tags.length === 0) newErrors.tags = 'Almeno un tag richiesto';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    const productData = {
      ...formData,
      price: Number(formData.price),
      ...(product && { _id: product._id })
    };

    onSave(productData);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="w-full max-w-4xl max-h-[90vh] overflow-y-auto glass-strong border border-white/20 rounded-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="sticky top-0 glass-strong border-b border-white/10 p-6 flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold holographic-text">
                  {product ? '✏️ Modifica Prodotto' : '➕ Nuovo Prodotto'}
                </h2>
                <p className="text-sm" style={{ color: 'var(--champagne)' }}>
                  {product ? 'Aggiorna le informazioni del prodotto' : 'Aggiungi un nuovo prodotto al catalogo'}
                </p>
              </div>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={onClose}
                className="w-10 h-10 glass rounded-full flex items-center justify-center text-white hover:bg-white/10 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </motion.button>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Nome Prodotto */}
                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Nome Prodotto *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className={`w-full glass border rounded-lg px-4 py-3 text-white placeholder-white/60 focus:outline-none focus:border-purple-500/50 transition-colors ${
                      errors.name ? 'border-red-500/50' : 'border-white/20'
                    }`}
                    placeholder="Es. Bot Telegram AI Premium"
                  />
                  {errors.name && <p className="text-red-400 text-xs mt-1">{errors.name}</p>}
                </div>

                {/* Prezzo */}
                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Prezzo (€) *
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.price}
                    onChange={(e) => handleInputChange('price', e.target.value)}
                    className={`w-full glass border rounded-lg px-4 py-3 text-white placeholder-white/60 focus:outline-none focus:border-purple-500/50 transition-colors ${
                      errors.price ? 'border-red-500/50' : 'border-white/20'
                    }`}
                    placeholder="299.99"
                  />
                  {errors.price && <p className="text-red-400 text-xs mt-1">{errors.price}</p>}
                </div>
              </div>

              {/* Descrizione */}
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Descrizione *
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={4}
                  className={`w-full glass border rounded-lg px-4 py-3 text-white placeholder-white/60 focus:outline-none focus:border-purple-500/50 transition-colors resize-none ${
                    errors.description ? 'border-red-500/50' : 'border-white/20'
                  }`}
                  placeholder="Descrizione dettagliata del prodotto..."
                />
                {errors.description && <p className="text-red-400 text-xs mt-1">{errors.description}</p>}
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Slug */}
                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Slug URL *
                  </label>
                  <input
                    type="text"
                    value={formData.slug}
                    onChange={(e) => handleInputChange('slug', e.target.value)}
                    className={`w-full glass border rounded-lg px-4 py-3 text-white placeholder-white/60 focus:outline-none focus:border-purple-500/50 transition-colors ${
                      errors.slug ? 'border-red-500/50' : 'border-white/20'
                    }`}
                    placeholder="bot-telegram-ai-premium"
                  />
                  {errors.slug && <p className="text-red-400 text-xs mt-1">{errors.slug}</p>}
                </div>

                {/* URL Immagine */}
                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    URL Immagine *
                  </label>
                  <input
                    type="url"
                    value={formData.image_url}
                    onChange={(e) => handleInputChange('image_url', e.target.value)}
                    className={`w-full glass border rounded-lg px-4 py-3 text-white placeholder-white/60 focus:outline-none focus:border-purple-500/50 transition-colors ${
                      errors.image_url ? 'border-red-500/50' : 'border-white/20'
                    }`}
                    placeholder="https://example.com/image.jpg"
                  />
                  {errors.image_url && <p className="text-red-400 text-xs mt-1">{errors.image_url}</p>}
                </div>
              </div>

              {/* Tags */}
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Tags/Categorie *
                </label>
                <div className="flex flex-wrap gap-2 mb-3">
                  {formData.tags.map((tag) => (
                    <motion.span
                      key={tag}
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="px-3 py-1 text-sm rounded-full border flex items-center space-x-2"
                      style={{
                        backgroundColor: 'rgba(142, 45, 226, 0.2)',
                        color: 'var(--ai-aura)',
                        borderColor: 'rgba(142, 45, 226, 0.3)'
                      }}
                    >
                      <span>{tag}</span>
                      <button
                        type="button"
                        onClick={() => removeTag(tag)}
                        className="text-red-400 hover:text-red-300"
                      >
                        ×
                      </button>
                    </motion.span>
                  ))}
                </div>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                    className="flex-1 glass border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/60 focus:outline-none focus:border-purple-500/50"
                    placeholder="Aggiungi tag..."
                  />
                  <motion.button
                    type="button"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={addTag}
                    className="px-4 py-2 rounded-lg font-medium text-white transition-all"
                    style={{ background: 'linear-gradient(135deg, var(--ai-aura), var(--cta))' }}
                  >
                    Aggiungi
                  </motion.button>
                </div>
                {errors.tags && <p className="text-red-400 text-xs mt-1">{errors.tags}</p>}
              </div>

              {/* Features */}
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Caratteristiche
                </label>
                <div className="flex flex-wrap gap-2 mb-3">
                  {formData.features.map((feature) => (
                    <motion.span
                      key={feature}
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="px-3 py-1 text-sm rounded-full border flex items-center space-x-2"
                      style={{
                        backgroundColor: 'rgba(0, 216, 182, 0.2)',
                        color: 'var(--cta)',
                        borderColor: 'rgba(0, 216, 182, 0.3)'
                      }}
                    >
                      <span>{feature}</span>
                      <button
                        type="button"
                        onClick={() => removeFeature(feature)}
                        className="text-red-400 hover:text-red-300"
                      >
                        ×
                      </button>
                    </motion.span>
                  ))}
                </div>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={newFeature}
                    onChange={(e) => setNewFeature(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}
                    className="flex-1 glass border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/60 focus:outline-none focus:border-purple-500/50"
                    placeholder="Aggiungi caratteristica..."
                  />
                  <motion.button
                    type="button"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={addFeature}
                    className="px-4 py-2 rounded-lg font-medium text-white transition-all"
                    style={{ background: 'linear-gradient(135deg, var(--cta), var(--accenti))' }}
                  >
                    Aggiungi
                  </motion.button>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-4 pt-6 border-t border-white/10">
                <motion.button
                  type="button"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={onClose}
                  className="flex-1 py-3 glass border border-white/20 rounded-xl text-white hover:bg-white/10 transition-colors"
                >
                  Annulla
                </motion.button>
                
                <motion.button
                  type="submit"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  disabled={isLoading}
                  className="flex-1 py-3 rounded-xl font-medium text-white transition-all duration-300 disabled:opacity-50"
                  style={{ 
                    background: 'linear-gradient(135deg, var(--ai-aura), var(--cta))',
                    color: 'var(--champagne)'
                  }}
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center space-x-2">
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                      />
                      <span>Salvando...</span>
                    </div>
                  ) : (
                    product ? 'Aggiorna Prodotto' : 'Crea Prodotto'
                  )}
                </motion.button>
              </div>
            </form>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

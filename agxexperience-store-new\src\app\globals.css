@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

/* 🎨 AGXexperience Store - TASK_DOCUMENT EXACT PALETTE */
:root {
  /* EXACT Color Palette from TASK_DOCUMENT */
  --sfondo-base: #0D0D0D;          /* Sfondo Base - Nero */
  --console: #181818;              /* Console - Grigio <PERSON>e */
  --ai-aura: #8E2DE2;              /* AI Aura - Viola Brillante */
  --accenti: #D4AF37;              /* Accenti - Oro Elegante */
  --cta: #00D8B6;                  /* CTA - Verde A<PERSON>qua */
  --champagne: #F7E9D7;            /* Champagne - Lusso */

  /* Legacy aliases for compatibility */
  --aurora-black: var(--sfondo-base);
  --aurora-charcoal: var(--console);
  --aurora-purple: var(--ai-aura);
  --aurora-gold: var(--accenti);
  --aurora-teal: var(--cta);
  --aurora-champagne: var(--champagne);

  /* Emotion Colors */
  --curiosity: #3B82F6;
  --excitement: #F59E0B;
  --focus: #6B7280;
  --discovery: #10B981;

  /* Audio Frequency */
  --frequency-432hz: 432;

  /* Glassmorphism */
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-blur: blur(10px);
}

@theme inline {
  --color-background: var(--aurora-black);
  --color-foreground: #FFFFFF;
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-mono: 'Poppins', system-ui, sans-serif;
}

body {
  background: linear-gradient(135deg, var(--aurora-black) 0%, #111111 50%, var(--aurora-charcoal) 100%);
  color: #FFFFFF;
  font-family: 'Inter', system-ui, sans-serif;
  overflow-x: hidden;
  background-attachment: fixed;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Poppins', system-ui, sans-serif;
  font-weight: 600;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--aurora-charcoal);
}

::-webkit-scrollbar-thumb {
  background: var(--aurora-purple);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--aurora-gold);
}

/* 💬 CHAT BOX GLASSMORPHISM - Bubble Trasparenti (TASK_DOCUMENT) */
.glass {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
}

.glass-strong {
  background: rgba(24, 24, 24, 0.8); /* Console color from TASK_DOCUMENT */
  backdrop-filter: blur(20px);
  border: 1px solid rgba(142, 45, 226, 0.2); /* AI Aura border */
}

/* Chat Bubble Trasparenti */
.chat-bubble-user {
  background: linear-gradient(135deg,
    rgba(0, 216, 182, 0.2) 0%,
    rgba(0, 216, 182, 0.1) 100%
  );
  backdrop-filter: blur(15px);
  border: 1px solid rgba(0, 216, 182, 0.3);
  border-radius: 20px 20px 5px 20px;
  box-shadow: 0 8px 32px rgba(0, 216, 182, 0.1);
  position: relative;
  overflow: hidden;
}

.chat-bubble-ai {
  background: linear-gradient(135deg,
    rgba(142, 45, 226, 0.2) 0%,
    rgba(142, 45, 226, 0.1) 100%
  );
  backdrop-filter: blur(15px);
  border: 1px solid rgba(142, 45, 226, 0.3);
  border-radius: 20px 20px 20px 5px;
  box-shadow: 0 8px 32px rgba(142, 45, 226, 0.1);
  position: relative;
  overflow: hidden;
}

/* Effetti Liquidi per Chat */
.chat-bubble-user::before,
.chat-bubble-ai::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  border-radius: inherit;
  z-index: -1;
  animation: bubble-shimmer 3s ease-in-out infinite;
}

@keyframes bubble-shimmer {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

/* Neural Wave Animation */
@keyframes neural-wave {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1) rotate(180deg);
    opacity: 0.7;
  }
  100% {
    transform: scale(1) rotate(360deg);
    opacity: 0.3;
  }
}

.neural-wave {
  animation: neural-wave 3s ease-in-out infinite;
}

/* 432Hz Frequency Pulse */
@keyframes frequency-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(142, 45, 226, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(142, 45, 226, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(142, 45, 226, 0);
  }
}

.frequency-pulse {
  animation: frequency-pulse 2s infinite;
}

/* 🌊 BACKGROUND ANIMATO - Griglia Liquida WebGL (TASK_DOCUMENT) */
.liquid-grid-bg {
  background: var(--sfondo-base);
  position: relative;
  overflow: hidden;
}

.liquid-grid-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(142, 45, 226, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(0, 216, 182, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(212, 175, 55, 0.1) 0%, transparent 50%);
  animation: liquid-flow 20s ease-in-out infinite;
  opacity: 0.6;
}

.liquid-grid-bg::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(90deg, rgba(142, 45, 226, 0.03) 1px, transparent 1px),
    linear-gradient(rgba(142, 45, 226, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-shift 15s linear infinite;
  opacity: 0.4;
}

@keyframes liquid-flow {
  0%, 100% {
    transform: translate(0, 0) scale(1);
    filter: hue-rotate(0deg);
  }
  33% {
    transform: translate(-10px, -10px) scale(1.05);
    filter: hue-rotate(60deg);
  }
  66% {
    transform: translate(10px, -5px) scale(0.95);
    filter: hue-rotate(-30deg);
  }
}

@keyframes grid-shift {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

/* Consciousness Awakening */
@keyframes consciousness-awakening {
  0% {
    opacity: 0;
    transform: scale(0.5);
    filter: blur(20px);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
    filter: blur(5px);
  }
  100% {
    opacity: 1;
    transform: scale(1);
    filter: blur(0px);
  }
}

.consciousness-awakening {
  animation: consciousness-awakening 2s ease-out forwards;
}

/* Emotion State Colors */
.emotion-curiosity {
  background: linear-gradient(135deg, var(--curiosity), #8B5CF6);
}

.emotion-excitement {
  background: linear-gradient(135deg, var(--excitement), #F97316);
}

.emotion-focus {
  background: linear-gradient(135deg, var(--focus), #4B5563);
}

.emotion-discovery {
  background: linear-gradient(135deg, var(--discovery), #059669);
}

/* Aurora Gradient - TASK_DOCUMENT Colors */
.aurora-gradient {
  background: linear-gradient(135deg, var(--aurora-purple), var(--aurora-teal));
}

.aurora-gradient-gold {
  background: linear-gradient(135deg, var(--aurora-purple), var(--aurora-gold));
}

.aurora-cta {
  background: var(--aurora-teal);
}

/* Text Gradients - TASK_DOCUMENT Palette */
.text-aurora {
  background: linear-gradient(135deg, var(--aurora-purple), var(--aurora-gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-price {
  color: var(--aurora-teal);
  text-shadow: 0 0 10px rgba(0, 216, 182, 0.3);
}

.text-luxury {
  color: var(--aurora-champagne);
}

/* Button Styles */
.btn-aurora {
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 500;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, var(--aurora-purple), #4A90E2);
  color: white;
  border: none;
  cursor: pointer;
}

.btn-aurora:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(142, 45, 226, 0.3);
}

/* 🌐 VETRINA PRODOTTI 3D - Card Rotabili con Glass Effect (TASK_DOCUMENT) */
.card-aurora {
  background: linear-gradient(135deg,
    rgba(24, 24, 24, 0.9) 0%,
    rgba(24, 24, 24, 0.7) 100%
  );
  backdrop-filter: blur(20px);
  border: 1px solid rgba(142, 45, 226, 0.3);
  border-radius: 1.5rem;
  padding: 2rem;
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  transform-style: preserve-3d;
}

.card-aurora::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(142, 45, 226, 0.2),
    transparent
  );
  transition: left 0.8s ease;
}

.card-aurora:hover::before {
  left: 100%;
}

.card-aurora:hover {
  transform: translateY(-10px) rotateX(5deg) rotateY(5deg);
  border-color: rgba(0, 216, 182, 0.5);
  box-shadow:
    0 20px 40px rgba(142, 45, 226, 0.2),
    0 0 60px rgba(0, 216, 182, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Effetto Rotazione 3D per Prodotti */
.product-card-3d {
  perspective: 1000px;
  transform-style: preserve-3d;
}

.product-card-3d:hover .card-aurora {
  transform: rotateY(10deg) rotateX(5deg) translateZ(20px);
}

.card-aurora:hover {
  border-color: var(--aurora-purple);
  box-shadow: 0 10px 30px rgba(142, 45, 226, 0.2);
  transform: translateY(-5px);
}

/* Range Slider Styles */
.slider {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

.slider::-webkit-slider-track {
  background: #374151;
  height: 8px;
  border-radius: 4px;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: var(--aurora-purple);
  height: 20px;
  width: 20px;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(142, 45, 226, 0.3);
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  background: var(--aurora-gold);
  transform: scale(1.1);
}

.slider::-moz-range-track {
  background: #374151;
  height: 8px;
  border-radius: 4px;
  border: none;
}

.slider::-moz-range-thumb {
  background: var(--aurora-purple);
  height: 20px;
  width: 20px;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(142, 45, 226, 0.3);
  transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
  background: var(--aurora-gold);
  transform: scale(1.1);
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Sticky positioning */
.sticky {
  position: sticky;
}

/* ⚡ EFFETTI FUTURISTICI - SITO DEL SECOLO */

/* Holographic Text Effect */
.holographic-text {
  background: linear-gradient(45deg,
    var(--ai-aura),
    var(--cta),
    var(--accenti),
    var(--ai-aura)
  );
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: holographic-shift 3s ease-in-out infinite;
  text-shadow: 0 0 30px rgba(142, 45, 226, 0.5);
}

@keyframes holographic-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Neural Network Lines */
.neural-lines {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(90deg, transparent 49%, rgba(142, 45, 226, 0.1) 50%, transparent 51%),
    linear-gradient(0deg, transparent 49%, rgba(0, 216, 182, 0.1) 50%, transparent 51%);
  background-size: 100px 100px;
  animation: neural-pulse 4s ease-in-out infinite;
  pointer-events: none;
}

@keyframes neural-pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

/* Quantum Glow Effect */
.quantum-glow {
  position: relative;
  overflow: hidden;
}

.quantum-glow::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    from 0deg,
    transparent,
    rgba(142, 45, 226, 0.1),
    transparent,
    rgba(0, 216, 182, 0.1),
    transparent
  );
  animation: quantum-rotation 8s linear infinite;
  pointer-events: none;
}

@keyframes quantum-rotation {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Hover effects */
.hover\:scale-\[1\.02\]:hover {
  transform: scale(1.02);
}

/* 3D Carousel Effects */
.perspective-1000 {
  perspective: 1000px;
}

.preserve-3d {
  transform-style: preserve-3d;
}

/* Apple Coverflow Style */
.coverflow-item {
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: center center;
}

.coverflow-center {
  transform: scale(1.2) translateZ(0px);
  filter: brightness(1.1) drop-shadow(0 20px 40px rgba(142, 45, 226, 0.3));
  z-index: 10;
}

.coverflow-side {
  filter: brightness(0.8);
}

// Test completo del progetto AGXexperience Store
const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

// Simulazione delle API implementate
const mockDatabase = {
  products: [
    {
      _id: '1',
      name: "Bot Telegram AI",
      description: "Assistente AI personalizzato per Telegram che automatizza le tue conversazioni e gestisce le richieste dei clienti 24/7. Integrazione completa con OpenAI e funzionalità avanzate di natural language processing.",
      price: 299,
      slug: "bot-telegram-ai",
      image_url: "/images/telegram-bot.jpg",
      tags: ["ai", "automation", "telegram", "chatbot", "customer-service"],
      created_at: new Date()
    },
    {
      _id: '2',
      name: "Landing Page Portfolio",
      description: "Sito vetrina professionale responsive con design moderno, ottimizzato SEO e integrazione CMS. Include animazioni fluide, contact forms e analytics integrati per massimizzare le conversioni.",
      price: 599,
      slug: "landing-page-portfolio",
      image_url: "/images/portfolio-landing.jpg", 
      tags: ["web-design", "portfolio", "responsive", "seo", "cms"],
      created_at: new Date()
    },
    {
      _id: '3',
      name: "Automazione Notion + Google Sheets",
      description: "Sistema di workflow automation che sincronizza automaticamente i tuoi dati tra Notion e Google Sheets. Include trigger personalizzati, report automatici e dashboard real-time.",
      price: 199,
      slug: "automazione-notion-sheets",
      image_url: "/images/notion-automation.jpg",
      tags: ["automation", "notion", "google-sheets", "workflow", "productivity"],
      created_at: new Date()
    }
  ],
  user_profiles: {},
  messages: [],
  orders: []
};

// AURORA AI Responses (simulazione Together.ai)
function getAuroraResponse(message, userProfile) {
  const responses = {
    greeting: [
      "Ciao! Sono AURORA, la tua designer personale AI. Cosa posso creare per te oggi? ✨",
      "Benvenuto nel futuro dello shopping! Sono AURORA, e sono qui per trasformare le tue idee in realtà digitali. 🚀"
    ],
    bot_interest: [
      "Fantastico! Vedo che sei interessato all'automazione. Il nostro Bot Telegram AI è perfetto per te - può gestire conversazioni 24/7 con intelligenza artificiale avanzata.",
      "Un bot Telegram? Eccellente scelta! Il nostro sistema AI può creare un assistente personalizzato che comprende il linguaggio naturale e si adatta al tuo business."
    ],
    web_interest: [
      "Perfetto! Per la tua presenza online, la nostra Landing Page Portfolio è la soluzione ideale. Design moderno, SEO ottimizzato e completamente responsive.",
      "Un sito web professionale? La nostra Landing Page Portfolio include tutto: design elegante, CMS integrato e analytics avanzati."
    ],
    automation_interest: [
      "Ottima idea! L'Automazione Notion + Google Sheets è perfetta per ottimizzare il tuo workflow. Sincronizzazione automatica e report in tempo reale.",
      "Automazione? Fantastico! Il nostro sistema collega Notion e Google Sheets con trigger personalizzati e dashboard intelligenti."
    ],
    general: [
      "Interessante! Dimmi di più sui tuoi obiettivi, così posso suggerirti la soluzione perfetta.",
      "Perfetto! Sono qui per aiutarti a trovare esattamente quello che stai cercando. Quali sono le tue priorità?",
      "Eccellente! Ogni progetto è unico, e io sono qui per creare qualcosa di speciale per te."
    ]
  };

  const lowerMessage = message.toLowerCase();
  
  if (lowerMessage.includes('ciao') || lowerMessage.includes('hello') || lowerMessage.includes('salve')) {
    return responses.greeting[Math.floor(Math.random() * responses.greeting.length)];
  }
  if (lowerMessage.includes('bot') || lowerMessage.includes('telegram') || lowerMessage.includes('chat')) {
    return responses.bot_interest[Math.floor(Math.random() * responses.bot_interest.length)];
  }
  if (lowerMessage.includes('sito') || lowerMessage.includes('web') || lowerMessage.includes('portfolio')) {
    return responses.web_interest[Math.floor(Math.random() * responses.web_interest.length)];
  }
  if (lowerMessage.includes('automazione') || lowerMessage.includes('notion') || lowerMessage.includes('sheets')) {
    return responses.automation_interest[Math.floor(Math.random() * responses.automation_interest.length)];
  }
  
  return responses.general[Math.floor(Math.random() * responses.general.length)];
}

// Determina emotion state
function getEmotionState(message) {
  const lowerMessage = message.toLowerCase();
  
  if (lowerMessage.includes('voglio') || lowerMessage.includes('ho bisogno')) return 'focus';
  if (lowerMessage.includes('fantastico') || lowerMessage.includes('wow')) return 'excitement';
  if (lowerMessage.includes('come') || lowerMessage.includes('cosa')) return 'curiosity';
  
  return 'discovery';
}

// Server principale
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  // CORS Headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // API Routes
  if (path === '/api/products' && method === 'GET') {
    res.setHeader('Content-Type', 'application/json');
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      data: mockDatabase.products
    }));
    return;
  }

  if (path === '/api/chat' && method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk.toString());
    
    req.on('end', () => {
      try {
        const { message, session_id } = JSON.parse(body);
        
        // Get or create user profile
        if (!mockDatabase.user_profiles[session_id]) {
          mockDatabase.user_profiles[session_id] = {
            session_id,
            interests: [],
            past_requests: [],
            preferences: { communication_style: 'casual', product_categories: [], price_range: 'any' },
            interaction_count: 0,
            last_seen: new Date(),
            emotional_profile: { current_mood: 'curiosity', engagement_level: 0.5, conversation_depth: 'surface' }
          };
        }
        
        const userProfile = mockDatabase.user_profiles[session_id];
        userProfile.interaction_count++;
        userProfile.last_seen = new Date();
        userProfile.past_requests.push({ request: message, timestamp: new Date() });
        
        const emotionState = getEmotionState(message);
        const response = getAuroraResponse(message, userProfile);
        
        // Save message
        mockDatabase.messages.push({
          session_id,
          user_message: message,
          ai_response: response,
          timestamp: new Date(),
          emotion_state: emotionState
        });
        
        res.setHeader('Content-Type', 'application/json');
        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          data: {
            response: response,
            emotion_state: emotionState,
            session_id: session_id
          }
        }));
      } catch (error) {
        res.setHeader('Content-Type', 'application/json');
        res.writeHead(400);
        res.end(JSON.stringify({ success: false, error: 'Invalid JSON' }));
      }
    });
    return;
  }

  if (path === '/api/checkout' && method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk.toString());
    
    req.on('end', () => {
      try {
        const { product_id } = JSON.parse(body);
        const product = mockDatabase.products.find(p => p._id === product_id);
        
        if (!product) {
          res.setHeader('Content-Type', 'application/json');
          res.writeHead(404);
          res.end(JSON.stringify({ success: false, error: 'Product not found' }));
          return;
        }

        const sessionId = `cs_mock_${Date.now()}`;
        mockDatabase.orders.push({
          product_id,
          stripe_session_id: sessionId,
          status: 'pending',
          created_at: new Date()
        });

        res.setHeader('Content-Type', 'application/json');
        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          data: {
            checkout_url: `https://checkout.stripe.com/mock-session-${sessionId}`,
            session_id: sessionId
          }
        }));
      } catch (error) {
        res.setHeader('Content-Type', 'application/json');
        res.writeHead(400);
        res.end(JSON.stringify({ success: false, error: 'Invalid JSON' }));
      }
    });
    return;
  }

  if (path === '/api/init' && method === 'POST') {
    res.setHeader('Content-Type', 'application/json');
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      message: 'AGXexperience Store Database inizializzato con successo!',
      data: {
        products_count: mockDatabase.products.length,
        users_count: Object.keys(mockDatabase.user_profiles).length,
        messages_count: mockDatabase.messages.length,
        orders_count: mockDatabase.orders.length,
        timestamp: new Date().toISOString()
      }
    }));
    return;
  }

  // Serve main application
  if (path === '/' || path === '/test') {
    res.setHeader('Content-Type', 'text/html');
    res.writeHead(200);
    res.end(`
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 AGXexperience Store - AURORA AI System</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Inter', system-ui, sans-serif; 
            background: linear-gradient(135deg, #0D0D0D 0%, #111111 50%, #181818 100%);
            color: white; 
            min-height: 100vh;
            overflow-x: hidden;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 40px; padding: 40px 0; }
        .header h1 { font-size: 3rem; font-weight: 700; margin-bottom: 10px; }
        .header .highlight { color: #8E2DE2; }
        .header p { font-size: 1.2rem; color: #ccc; }
        .aurora-avatar { 
            width: 120px; height: 120px; 
            background: linear-gradient(135deg, #8E2DE2, #4A90E2);
            border-radius: 50%; 
            display: flex; align-items: center; justify-content: center;
            font-size: 3rem; font-weight: bold;
            margin: 20px auto;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(142, 45, 226, 0.7); }
            70% { transform: scale(1.05); box-shadow: 0 0 0 10px rgba(142, 45, 226, 0); }
            100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(142, 45, 226, 0); }
        }
        .status { text-align: center; margin: 20px 0; }
        .status .online { color: #00D8B6; }
        .chat-container { 
            background: rgba(255,255,255,0.05); 
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
            border-radius: 20px; 
            padding: 30px; 
            margin: 30px 0; 
            min-height: 400px;
        }
        .messages { height: 300px; overflow-y: auto; margin-bottom: 20px; }
        .message { 
            margin: 15px 0; 
            padding: 15px; 
            border-radius: 15px; 
            max-width: 80%;
        }
        .message.user { 
            background: linear-gradient(135deg, #4A90E2, #357ABD);
            margin-left: auto; 
            text-align: right;
        }
        .message.aurora { 
            background: linear-gradient(135deg, #8E2DE2, #B45BCF);
        }
        .message .sender { font-size: 0.8rem; opacity: 0.8; margin-bottom: 5px; }
        .input-area { display: flex; gap: 15px; }
        .input-area input { 
            flex: 1; 
            padding: 15px; 
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 10px; 
            background: rgba(255,255,255,0.1);
            color: white; 
            font-size: 1rem;
        }
        .input-area input::placeholder { color: rgba(255,255,255,0.6); }
        .input-area button { 
            padding: 15px 30px; 
            background: linear-gradient(135deg, #8E2DE2, #4A90E2);
            color: white; 
            border: none; 
            border-radius: 10px; 
            cursor: pointer; 
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .input-area button:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(142, 45, 226, 0.4); }
        .test-section { 
            background: rgba(255,255,255,0.05); 
            padding: 25px; 
            margin: 25px 0; 
            border-radius: 15px; 
            border: 1px solid rgba(255,255,255,0.1);
        }
        .test-section h3 { margin-bottom: 15px; color: #8E2DE2; }
        .test-button { 
            background: linear-gradient(135deg, #00D8B6, #00A693);
            color: white; 
            border: none; 
            padding: 12px 25px; 
            border-radius: 8px; 
            cursor: pointer; 
            margin: 8px; 
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .test-button:hover { transform: translateY(-1px); }
        .result { 
            background: rgba(0,0,0,0.4); 
            padding: 15px; 
            margin: 15px 0; 
            border-radius: 8px; 
            font-family: 'Courier New', monospace; 
            font-size: 0.9rem;
            border-left: 4px solid #8E2DE2;
        }
        .footer { text-align: center; margin-top: 50px; padding: 30px; border-top: 1px solid rgba(255,255,255,0.1); }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 AGX<span class="highlight">experience</span> Store</h1>
            <p>Il primo eCommerce AI-driven che ti ascolta, ti capisce, e ti guida</p>
            <div class="aurora-avatar">A</div>
            <div class="status">
                <span class="online">● AURORA Online</span> • 432Hz Frequency • Neural Wave Active
            </div>
        </div>

        <div class="chat-container">
            <h3 style="color: #8E2DE2; margin-bottom: 20px;">💬 Chat con AURORA</h3>
            <div class="messages" id="messages">
                <div class="message aurora">
                    <div class="sender">AURORA</div>
                    <div>Ciao! Sono AURORA, la tua designer personale AI. Cosa posso creare per te oggi? ✨</div>
                </div>
            </div>
            <div class="input-area">
                <input type="text" id="chat-input" placeholder="Dimmi cosa desideri creare..." onkeypress="if(event.key==='Enter') sendMessage()">
                <button onclick="sendMessage()">Invia</button>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 Test Sistema</h3>
            <button class="test-button" onclick="testInit()">Inizializza Database</button>
            <button class="test-button" onclick="testProducts()">Carica Prodotti</button>
            <button class="test-button" onclick="testCheckout()">Test Checkout</button>
            <div id="test-result" class="result" style="display: none;"></div>
        </div>

        <div class="footer">
            <p>© 2025 AGXexperience Store - Designed with ❤️ by AURORA</p>
            <p style="margin-top: 10px; font-size: 0.9rem; opacity: 0.7;">
                Powered by Together.ai • MongoDB • Stripe • Next.js
            </p>
        </div>
    </div>

    <script>
        let sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

        async function sendMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();
            if (!message) return;

            const messagesDiv = document.getElementById('messages');
            
            // Add user message
            messagesDiv.innerHTML += \`
                <div class="message user">
                    <div class="sender">Tu</div>
                    <div>\${message}</div>
                </div>
            \`;

            input.value = '';
            messagesDiv.scrollTop = messagesDiv.scrollHeight;

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message, session_id: sessionId })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    messagesDiv.innerHTML += \`
                        <div class="message aurora">
                            <div class="sender">AURORA • \${data.data.emotion_state}</div>
                            <div>\${data.data.response}</div>
                        </div>
                    \`;
                } else {
                    messagesDiv.innerHTML += \`
                        <div class="message aurora">
                            <div class="sender">AURORA • Error</div>
                            <div>Mi dispiace, ho avuto un problema tecnico. Riprova!</div>
                        </div>
                    \`;
                }
            } catch (error) {
                messagesDiv.innerHTML += \`
                    <div class="message aurora">
                        <div class="sender">AURORA • Error</div>
                        <div>Errore di connessione. Riprova!</div>
                    </div>
                \`;
            }
            
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        async function testInit() {
            try {
                const response = await fetch('/api/init', { method: 'POST' });
                const data = await response.json();
                showTestResult(JSON.stringify(data, null, 2));
            } catch (error) {
                showTestResult('Errore: ' + error.message);
            }
        }

        async function testProducts() {
            try {
                const response = await fetch('/api/products');
                const data = await response.json();
                showTestResult(JSON.stringify(data, null, 2));
            } catch (error) {
                showTestResult('Errore: ' + error.message);
            }
        }

        async function testCheckout() {
            try {
                const response = await fetch('/api/checkout', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ product_id: '1' })
                });
                const data = await response.json();
                showTestResult(JSON.stringify(data, null, 2));
            } catch (error) {
                showTestResult('Errore: ' + error.message);
            }
        }

        function showTestResult(result) {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = result;
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
    `);
    return;
  }

  // 404
  res.writeHead(404);
  res.end('Not Found');
});

const PORT = 3001;
server.listen(PORT, () => {
  console.log(`🚀 AGXexperience Store - Full Project Test`);
  console.log(`📱 URL: http://localhost:${PORT}`);
  console.log(`🤖 AURORA AI System: ONLINE`);
  console.log(`💾 Database: Mock MongoDB Ready`);
  console.log(`🎨 Design System: Neo-Glass + Dreamwave`);
  console.log(`🔊 Audio Frequency: 432Hz`);
  console.log(`✨ Status: ALL SYSTEMS OPERATIONAL`);
});

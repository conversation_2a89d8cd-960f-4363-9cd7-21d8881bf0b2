import { NextRequest, NextResponse } from 'next/server';
import { getDatabase, COLLECTIONS } from '@/lib/mongodb';
import { Product, ApiResponse } from '@/types';

// GET - Fetch all products
export async function GET(request: NextRequest) {
  try {
    const db = await getDatabase();
    const products = await db.collection(COLLECTIONS.PRODUCTS)
      .find({})
      .sort({ created_at: -1 })
      .toArray();

    return NextResponse.json<ApiResponse<Product[]>>({
      success: true,
      data: products
    });

  } catch (error) {
    console.error('Products GET Error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to fetch products'
    }, { status: 500 });
  }
}

// POST - Create new product (Admin only)
export async function POST(request: NextRequest) {
  try {
    const productData = await request.json();
    
    // Validate required fields
    const { name, description, price, slug, image_url, tags } = productData;
    
    if (!name || !description || !price || !slug || !image_url) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Missing required fields'
      }, { status: 400 });
    }

    const db = await getDatabase();
    
    // Check if slug already exists
    const existingProduct = await db.collection(COLLECTIONS.PRODUCTS)
      .findOne({ slug });
    
    if (existingProduct) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Product with this slug already exists'
      }, { status: 409 });
    }

    const newProduct: Product = {
      name,
      description,
      price: Number(price),
      slug,
      image_url,
      tags: tags || [],
      created_at: new Date()
    };

    const result = await db.collection(COLLECTIONS.PRODUCTS).insertOne(newProduct);
    
    return NextResponse.json<ApiResponse<Product>>({
      success: true,
      data: { ...newProduct, _id: result.insertedId.toString() },
      message: 'Product created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Products POST Error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to create product'
    }, { status: 500 });
  }
}

// Initialize with mock products if collection is empty
export async function initializeMockProducts() {
  try {
    const db = await getDatabase();
    const count = await db.collection(COLLECTIONS.PRODUCTS).countDocuments();
    
    if (count === 0) {
      const mockProducts: Product[] = [
        {
          name: "Bot Telegram AI",
          description: "Assistente AI personalizzato per Telegram che automatizza le tue conversazioni e gestisce le richieste dei clienti 24/7. Integrazione completa con OpenAI e funzionalità avanzate di natural language processing.",
          price: 299,
          slug: "bot-telegram-ai",
          image_url: "/images/telegram-bot.jpg",
          tags: ["ai", "automation", "telegram", "chatbot", "customer-service"],
          created_at: new Date()
        },
        {
          name: "Landing Page Portfolio",
          description: "Sito vetrina professionale responsive con design moderno, ottimizzato SEO e integrazione CMS. Include animazioni fluide, contact forms e analytics integrati per massimizzare le conversioni.",
          price: 599,
          slug: "landing-page-portfolio",
          image_url: "/images/portfolio-landing.jpg", 
          tags: ["web-design", "portfolio", "responsive", "seo", "cms"],
          created_at: new Date()
        },
        {
          name: "Automazione Notion + Google Sheets",
          description: "Sistema di workflow automation che sincronizza automaticamente i tuoi dati tra Notion e Google Sheets. Include trigger personalizzati, report automatici e dashboard real-time.",
          price: 199,
          slug: "automazione-notion-sheets",
          image_url: "/images/notion-automation.jpg",
          tags: ["automation", "notion", "google-sheets", "workflow", "productivity"],
          created_at: new Date()
        }
      ];

      await db.collection(COLLECTIONS.PRODUCTS).insertMany(mockProducts);
      console.log('Mock products initialized successfully');
    }
  } catch (error) {
    console.error('Failed to initialize mock products:', error);
  }
}

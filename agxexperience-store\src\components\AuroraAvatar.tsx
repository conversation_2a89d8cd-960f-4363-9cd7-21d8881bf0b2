'use client';

import { useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { EmotionState } from '@/types';

interface AuroraAvatarProps {
  emotionState: EmotionState;
  isListening?: boolean;
  isSpeaking?: boolean;
}

export default function AuroraAvatar({ 
  emotionState, 
  isListening = false, 
  isSpeaking = false 
}: AuroraAvatarProps) {
  
  const getEmotionGradient = (emotion: EmotionState) => {
    switch (emotion) {
      case 'curiosity':
        return 'from-blue-400 via-purple-500 to-indigo-600';
      case 'excitement':
        return 'from-yellow-400 via-orange-500 to-red-500';
      case 'focus':
        return 'from-gray-400 via-slate-500 to-gray-600';
      case 'discovery':
        return 'from-green-400 via-teal-500 to-cyan-600';
      default:
        return 'from-purple-400 via-pink-500 to-purple-600';
    }
  };

  const getEmotionAnimation = (emotion: EmotionState) => {
    switch (emotion) {
      case 'curiosity':
        return {
          scale: [1, 1.05, 1],
          rotate: [0, 2, -2, 0],
        };
      case 'excitement':
        return {
          scale: [1, 1.1, 1],
          y: [0, -5, 0],
        };
      case 'focus':
        return {
          scale: [1, 0.98, 1],
          opacity: [1, 0.9, 1],
        };
      case 'discovery':
        return {
          scale: [1, 1.03, 1],
          rotate: [0, 5, -5, 0],
        };
      default:
        return {
          scale: [1, 1.02, 1],
        };
    }
  };

  return (
    <div className="relative flex items-center justify-center">
      {/* Outer Aura Ring */}
      <motion.div
        className="absolute inset-0 rounded-full"
        animate={{
          scale: isListening ? [1, 1.2, 1] : [1, 1.1, 1],
          opacity: [0.3, 0.6, 0.3],
        }}
        transition={{
          duration: isSpeaking ? 0.5 : 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        style={{
          background: `conic-gradient(from 0deg, transparent, rgba(139, 69, 19, 0.3), transparent)`,
          filter: 'blur(20px)',
        }}
      />

      {/* Middle Aura Ring */}
      <motion.div
        className="absolute inset-2 rounded-full"
        animate={{
          scale: isListening ? [1, 1.15, 1] : [1, 1.05, 1],
          opacity: [0.4, 0.7, 0.4],
        }}
        transition={{
          duration: isSpeaking ? 0.3 : 1.5,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 0.2
        }}
        style={{
          background: `conic-gradient(from 45deg, transparent, rgba(139, 69, 19, 0.4), transparent)`,
          filter: 'blur(15px)',
        }}
      />

      {/* Main Avatar Container */}
      <motion.div
        className="relative w-32 h-32 rounded-full overflow-hidden border-2 border-white/20 backdrop-blur-sm"
        animate={getEmotionAnimation(emotionState)}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        style={{
          background: `linear-gradient(135deg, ${getEmotionGradient(emotionState).replace('from-', '').replace('via-', '').replace('to-', '').split(' ').join(', ')})`,
        }}
      >
        {/* Inner Glow */}
        <div 
          className="absolute inset-0 rounded-full opacity-60"
          style={{
            background: `radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3), transparent 50%)`,
          }}
        />

        {/* AURORA Logo/Icon */}
        <div className="absolute inset-0 flex items-center justify-center">
          <motion.div
            animate={isSpeaking ? {
              scale: [1, 1.1, 1],
              rotate: [0, 5, -5, 0]
            } : {}}
            transition={{ duration: 0.5, repeat: isSpeaking ? Infinity : 0 }}
            className="text-white text-4xl font-bold"
          >
            A
          </motion.div>
        </div>

        {/* Voice Ripples when speaking */}
        {isSpeaking && (
          <>
            <motion.div
              className="absolute inset-0 rounded-full border-2 border-white/30"
              animate={{
                scale: [1, 1.5],
                opacity: [0.6, 0],
              }}
              transition={{
                duration: 0.8,
                repeat: Infinity,
                ease: "easeOut"
              }}
            />
            <motion.div
              className="absolute inset-0 rounded-full border-2 border-white/20"
              animate={{
                scale: [1, 1.8],
                opacity: [0.4, 0],
              }}
              transition={{
                duration: 1,
                repeat: Infinity,
                ease: "easeOut",
                delay: 0.2
              }}
            />
          </>
        )}

        {/* Listening Pulse */}
        {isListening && (
          <motion.div
            className="absolute inset-0 rounded-full bg-white/10"
            animate={{
              scale: [1, 1.1, 1],
              opacity: [0, 0.3, 0],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        )}
      </motion.div>

      {/* Emotion State Indicator */}
      <motion.div
        className="absolute -bottom-2 left-1/2 transform -translate-x-1/2"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="px-3 py-1 bg-black/50 backdrop-blur-sm rounded-full border border-white/20">
          <span className="text-xs text-white/80 font-medium capitalize">
            {emotionState}
          </span>
        </div>
      </motion.div>
    </div>
  );
}

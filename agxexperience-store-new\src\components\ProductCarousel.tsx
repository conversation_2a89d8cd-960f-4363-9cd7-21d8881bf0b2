'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Product } from '@/types';


interface ProductCarouselProps {
  products: Product[];
  onProductSelect: (product: Product) => void;
  mode: 'catalog' | 'suggestions' | 'single';
}

export default function ProductCarousel({
  products,
  onProductSelect,
  mode
}: ProductCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(mode === 'catalog');
  const filteredProducts = products;

  // Auto-play carousel
  useEffect(() => {
    if (!isAutoPlaying || filteredProducts.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % filteredProducts.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, filteredProducts.length]);

  const getProductIcon = (slug: string) => {
    if (slug.includes('telegram')) return '🤖';
    if (slug.includes('portfolio')) return '🌐';
    if (slug.includes('notion')) return '⚡';
    return '💎';
  };

  const nextProduct = () => {
    setCurrentIndex((prev) => (prev + 1) % filteredProducts.length);
  };

  const prevProduct = () => {
    setCurrentIndex((prev) => (prev - 1 + filteredProducts.length) % filteredProducts.length);
  };



  if (products.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="text-6xl mb-4 opacity-50">🛍️</div>
          <p className="text-gray-400">Nessun prodotto disponibile</p>
        </div>
      </div>
    );
  }

  if (mode === 'single' && products.length === 1) {
    const product = products[0];
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="h-full flex flex-col"
      >
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-white mb-2">Prodotto Suggerito</h2>
          <div className="w-12 h-1 bg-aurora rounded-full"></div>
        </div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          onClick={() => onProductSelect(product)}
          className="card-aurora cursor-pointer flex-1 flex flex-col"
        >
          {/* Product Image */}
          <div className="relative w-full h-64 bg-gradient-to-br from-purple-600/20 to-blue-600/20 rounded-xl mb-4 flex items-center justify-center overflow-hidden">
            <motion.div
              whileHover={{ scale: 1.1 }}
              className="text-8xl opacity-70"
            >
              {getProductIcon(product.slug)}
            </motion.div>
          </div>

          {/* Product Info */}
          <div className="flex-1">
            <h3 className="text-xl font-bold text-white mb-2">
              {product.name}
            </h3>

            {/* Pulsante Dettagli Elegante */}
            <motion.button
              whileHover={{ scale: 1.02, x: 2 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => {
                console.log('🔍 Dettagli clicked for:', product.name);
                onProductSelect?.(product);
              }}
              className="inline-flex items-center space-x-1 text-xs text-white/70 hover:text-white transition-colors mb-3 group"
            >
              <span className="border-b border-dotted border-white/30 group-hover:border-white/60 transition-colors">
                Visualizza dettagli
              </span>
              <svg className="w-3 h-3 transform group-hover:translate-x-0.5 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </motion.button>

            <p className="text-gray-300 text-sm leading-relaxed line-clamp-2">
              {product.description}
            </p>
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-2 mt-4">
            {product.tags.slice(0, 3).map((tag) => (
              <span
                key={tag}
                className="px-2 py-1 bg-purple-600/20 text-purple-300 text-xs rounded-full border border-purple-500/30"
              >
                {tag}
              </span>
            ))}
          </div>
        </motion.div>
      </motion.div>
    );
  }

  // Debug
  console.log('ProductCarousel - Products:', products.length, 'Filtered:', filteredProducts.length);
  console.log('🎨 ProductCarousel rendering with modifications - no price, with details button');

  // Forza currentIndex a 0 per prodotto singolo
  useEffect(() => {
    if (filteredProducts.length === 1) {
      setCurrentIndex(0);
      console.log('Single product detected, setting currentIndex to 0');
    }
  }, [filteredProducts.length]);

  return (
    <div className="h-full flex flex-col">



      {/* Carousel 3D Container - Altezza Aumentata */}
      <div className="relative perspective-1000 h-64">
        {filteredProducts.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-white/60">
              <div className="text-4xl mb-2">🌟</div>
              <div className="text-sm">Caricamento prodotti...</div>
            </div>
          </div>
        ) : (
          <div
            className="relative w-full h-full overflow-visible"
            onMouseEnter={() => setIsAutoPlaying(false)}
            onMouseLeave={() => setIsAutoPlaying(mode === 'catalog')}
          >
            {/* 3D Carousel Items */}
            <div className="absolute inset-0">
              <div className="relative w-full h-full">
              {filteredProducts.map((product, index) => {
                const offset = index - currentIndex;
                const absOffset = Math.abs(offset);

                // Logica speciale per prodotto singolo
                const isSingleProduct = filteredProducts.length === 1;
                const isCenter = offset === 0 || isSingleProduct;

                // Calcolo posizione e scala per effetto 3D
                const scale = isCenter ? 1.2 : Math.max(0.6, 1 - absOffset * 0.2);
                const opacity = isCenter ? 1 : Math.max(0.3, 1 - absOffset * 0.3);
                const rotateY = isSingleProduct ? 0 : offset * 25; // No rotazione per prodotto singolo
                const translateX = isSingleProduct ? 0 : offset * 180; // Centrato per prodotto singolo
                const translateZ = isCenter ? 0 : -100 * absOffset; // Profondità

                console.log(`Product ${index}: offset=${offset}, isCenter=${isCenter}, isSingle=${isSingleProduct}`);

                return (
                  <motion.div
                    key={product.id || product._id || product.slug}
                    animate={{
                      scale,
                      opacity,
                      rotateY,
                      x: translateX,
                      z: translateZ,
                    }}
                    transition={{
                      duration: 0.6,
                      ease: [0.25, 0.46, 0.45, 0.94] // Apple-like easing
                    }}
                    className="absolute w-40 h-48 cursor-pointer"
                    style={{
                      transformStyle: 'preserve-3d',
                      filter: isCenter ? 'brightness(1.1) drop-shadow(0 20px 40px rgba(142, 45, 226, 0.3))' : 'brightness(0.8)',
                      left: '50%',
                      top: '50%',
                      marginLeft: '-80px', // metà della larghezza (w-40 = 160px)
                      marginTop: '-96px',  // metà dell'altezza (h-48 = 192px)
                    }}
                    onClick={() => {
                      if (isCenter) {
                        onProductSelect(product);
                      } else {
                        setCurrentIndex(index);
                      }
                    }}
                  >
                    {/* Card 3D Content */}
                    <div className="card-aurora h-full flex flex-col consciousness-awakening">
                      {/* Product Image 3D */}
                      <div
                        className="relative w-full h-28 rounded-md mb-2 flex items-center justify-center overflow-hidden"
                        style={{
                          background: `linear-gradient(135deg,
                            rgba(142, 45, 226, 0.2),
                            rgba(0, 216, 182, 0.2)
                          )`
                        }}
                      >
                        <motion.div
                          whileHover={isCenter ? { scale: 1.1, rotate: 5 } : {}}
                          transition={{ duration: 0.3 }}
                          className="text-3xl opacity-80"
                        >
                          {getProductIcon(product.slug)}
                        </motion.div>

                        {/* Price Badge con palette corretta */}
                        <div className="absolute top-1 right-1">
                          <div className="glass px-1 py-0.5 rounded-full border border-white/20">
                            <span
                              className="text-sm font-bold frequency-pulse"
                              style={{ color: 'var(--aurora-teal)' }}
                            >
                              €{product.price}
                            </span>
                          </div>
                        </div>

                        {/* Neural Wave Effect */}
                        <div
                          className="absolute inset-0 neural-wave opacity-10 rounded-xl"
                          style={{
                            background: `linear-gradient(45deg,
                              rgba(142, 45, 226, 0.1),
                              rgba(0, 216, 182, 0.1)
                            )`
                          }}
                        ></div>
                      </div>

                      {/* Product Info Compatto */}
                      <div className="flex-1 text-center px-1">
                        <h3
                          className="text-sm font-semibold mb-1 line-clamp-2"
                          style={{ color: 'var(--aurora-champagne)' }}
                        >
                          {product.name}
                        </h3>
                      </div>


                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>

          {/* Navigation Arrows 3D */}
          {filteredProducts.length > 1 && (
            <>
              <motion.button
                whileHover={{ scale: 1.1, x: -8 }}
                whileTap={{ scale: 0.9 }}
                onClick={prevProduct}
                className="absolute left-2 top-1/2 transform -translate-y-1/2 w-8 h-8 glass border border-white/20 rounded-full flex items-center justify-center text-white transition-all z-20 shadow-lg"
                style={{
                  background: 'linear-gradient(135deg, var(--aurora-purple), var(--aurora-teal))',
                  color: 'var(--aurora-champagne)'
                }}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.1, x: 8 }}
                whileTap={{ scale: 0.9 }}
                onClick={nextProduct}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 glass border border-white/20 rounded-full flex items-center justify-center text-white transition-all z-20 shadow-lg"
                style={{
                  background: 'linear-gradient(135deg, var(--aurora-purple), var(--aurora-teal))',
                  color: 'var(--aurora-champagne)'
                }}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </motion.button>
            </>
          )}
          </div>
        )}

        {/* Dots Indicator con palette corretta */}
        {filteredProducts.length > 1 && (
          <div className="flex justify-center mt-4 space-x-2">
            {filteredProducts.map((_, index) => (
              <motion.button
                key={index}
                whileHover={{ scale: 1.2 }}
                whileTap={{ scale: 0.8 }}
                onClick={() => setCurrentIndex(index)}
                className="w-2 h-2 rounded-full transition-all duration-300"
                style={{
                  backgroundColor: index === currentIndex ? 'var(--aurora-teal)' : 'var(--aurora-charcoal)',
                  boxShadow: index === currentIndex ? '0 0 10px rgba(0, 216, 182, 0.5)' : 'none'
                }}
              />
            ))}
          </div>
        )}

        {/* Messaggio per prodotto singolo */}
        {filteredProducts.length === 1 && (
          <div className="flex justify-center mt-4">
            <div className="px-4 py-2 glass border border-white/20 rounded-full">
              <span className="text-white/70 text-sm">Prodotto unico per questa categoria</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

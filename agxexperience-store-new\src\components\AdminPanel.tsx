'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Product } from '@/types';
import ProductForm from './ProductForm';

interface AdminPanelProps {
  isVisible: boolean;
  onClose: () => void;
}

export default function AdminPanel({ isVisible, onClose }: AdminPanelProps) {
  const [activeTab, setActiveTab] = useState<'products' | 'aurora' | 'analytics' | 'design'>('products');
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [isProductFormOpen, setIsProductFormOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [formLoading, setFormLoading] = useState(false);

  useEffect(() => {
    if (isVisible) {
      fetchProducts();
    }
  }, [isVisible]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/products');
      const data = await response.json();
      if (data.success) {
        setProducts(data.data.products || data.data);
      }
    } catch (error) {
      console.error('Failed to fetch products:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateProduct = () => {
    setEditingProduct(null);
    setIsProductFormOpen(true);
  };

  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setIsProductFormOpen(true);
  };

  const handleDeleteProduct = async (productId: string) => {
    if (!confirm('Sei sicuro di voler eliminare questo prodotto?')) return;

    try {
      const response = await fetch(`/api/products?id=${productId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        await fetchProducts(); // Refresh list
        alert('Prodotto eliminato con successo!');
      } else {
        alert('Errore nell\'eliminazione: ' + data.error);
      }
    } catch (error) {
      alert('Errore di rete durante l\'eliminazione');
    }
  };

  const handleSaveProduct = async (productData: Partial<Product>) => {
    try {
      setFormLoading(true);

      const method = editingProduct ? 'PUT' : 'POST';
      const response = await fetch('/api/products', {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      });

      const data = await response.json();

      if (data.success) {
        await fetchProducts(); // Refresh list
        setIsProductFormOpen(false);
        setEditingProduct(null);
        alert(editingProduct ? 'Prodotto aggiornato con successo!' : 'Prodotto creato con successo!');
      } else {
        alert('Errore nel salvare il prodotto: ' + data.error);
      }
    } catch (error) {
      alert('Errore di rete durante il salvataggio');
    } finally {
      setFormLoading(false);
    }
  };

  const tabs = [
    { id: 'products', label: 'Prodotti', icon: '🛍️' },
    { id: 'aurora', label: 'AURORA AI', icon: '🤖' },
    { id: 'analytics', label: 'Analytics', icon: '📊' },
    { id: 'design', label: 'Design', icon: '🎨' }
  ];

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="w-full max-w-6xl h-[90vh] glass-strong border border-white/20 rounded-2xl overflow-hidden flex flex-col"
          >
            {/* Header */}
            <div className="p-6 border-b border-white/10 flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold holographic-text">
                  🚀 AURORA Admin Panel
                </h1>
                <p className="text-sm" style={{ color: 'var(--champagne)' }}>
                  Pannello di controllo futuristico
                </p>
              </div>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={onClose}
                className="w-10 h-10 glass rounded-full flex items-center justify-center text-white hover:bg-white/10 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </motion.button>
            </div>

            {/* Tabs */}
            <div className="flex border-b border-white/10">
              {tabs.map((tab) => (
                <motion.button
                  key={tab.id}
                  whileHover={{ y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex-1 px-6 py-4 text-sm font-medium transition-all duration-300 ${
                    activeTab === tab.id
                      ? 'text-white border-b-2'
                      : 'text-gray-400 hover:text-white'
                  }`}
                  style={{
                    borderBottomColor: activeTab === tab.id ? 'var(--cta)' : 'transparent'
                  }}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </motion.button>
              ))}
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto p-6">
              <AnimatePresence mode="wait">
                {activeTab === 'products' && (
                  <motion.div
                    key="products"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    className="space-y-6"
                  >
                    <div className="flex items-center justify-between">
                      <h2 className="text-xl font-semibold text-white">Gestione Prodotti</h2>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={handleCreateProduct}
                        className="px-4 py-2 rounded-lg font-medium text-white transition-all"
                        style={{ background: 'linear-gradient(135deg, var(--ai-aura), var(--cta))' }}
                      >
                        + Nuovo Prodotto
                      </motion.button>
                    </div>

                    {loading ? (
                      <div className="flex items-center justify-center h-32">
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                          className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full"
                        />
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {products.map((product, index) => (
                          <motion.div
                            key={product._id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="card-aurora p-4"
                          >
                            <div className="flex items-start justify-between mb-3">
                              <h3 className="font-semibold text-white text-sm line-clamp-2">
                                {product.name}
                              </h3>
                              <div className="flex space-x-2">
                                <motion.button
                                  whileHover={{ scale: 1.1 }}
                                  whileTap={{ scale: 0.9 }}
                                  onClick={() => handleEditProduct(product)}
                                  className="w-8 h-8 glass rounded-full flex items-center justify-center text-white hover:bg-white/10"
                                  title="Modifica prodotto"
                                >
                                  ✏️
                                </motion.button>
                                <motion.button
                                  whileHover={{ scale: 1.1 }}
                                  whileTap={{ scale: 0.9 }}
                                  onClick={() => handleDeleteProduct(product._id!)}
                                  className="w-8 h-8 glass rounded-full flex items-center justify-center text-red-400 hover:bg-red-500/10"
                                  title="Elimina prodotto"
                                >
                                  🗑️
                                </motion.button>
                              </div>
                            </div>
                            <p className="text-gray-300 text-xs mb-3 line-clamp-2">
                              {product.description}
                            </p>
                            <div className="flex items-center justify-between">
                              <span 
                                className="text-lg font-bold"
                                style={{ color: 'var(--cta)' }}
                              >
                                €{product.price}
                              </span>
                              <div className="flex flex-wrap gap-1">
                                {product.tags.slice(0, 2).map((tag) => (
                                  <span
                                    key={tag}
                                    className="px-2 py-1 text-xs rounded-full"
                                    style={{
                                      backgroundColor: 'rgba(142, 45, 226, 0.2)',
                                      color: 'var(--ai-aura)'
                                    }}
                                  >
                                    {tag}
                                  </span>
                                ))}
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    )}
                  </motion.div>
                )}

                {activeTab === 'aurora' && (
                  <motion.div
                    key="aurora"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    className="space-y-6"
                  >
                    <h2 className="text-xl font-semibold text-white">Configurazione AURORA AI</h2>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div className="card-aurora p-6">
                        <h3 className="text-lg font-semibold text-white mb-4">Personalità</h3>
                        <div className="space-y-4">
                          <div>
                            <label className="block text-sm text-gray-300 mb-2">Tono di Voce</label>
                            <select className="w-full glass border border-white/20 rounded-lg px-4 py-2 text-white">
                              <option>Professionale</option>
                              <option>Amichevole</option>
                              <option>Entusiasta</option>
                            </select>
                          </div>
                          <div>
                            <label className="block text-sm text-gray-300 mb-2">Frequenza 432Hz</label>
                            <input 
                              type="range" 
                              min="400" 
                              max="500" 
                              defaultValue="432"
                              className="w-full"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="card-aurora p-6">
                        <h3 className="text-lg font-semibold text-white mb-4">Risposte Automatiche</h3>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-white">Suggerimenti Prodotti</span>
                            <div className="w-12 h-6 bg-purple-600 rounded-full"></div>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-white">Analisi Emotiva</span>
                            <div className="w-12 h-6 bg-purple-600 rounded-full"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}

                {activeTab === 'analytics' && (
                  <motion.div
                    key="analytics"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    className="space-y-6"
                  >
                    <h2 className="text-xl font-semibold text-white">Analytics Dashboard</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="card-aurora p-6 text-center">
                        <div className="text-3xl font-bold" style={{ color: 'var(--cta)' }}>
                          1,247
                        </div>
                        <div className="text-gray-300 text-sm">Conversazioni</div>
                      </div>
                      <div className="card-aurora p-6 text-center">
                        <div className="text-3xl font-bold" style={{ color: 'var(--accenti)' }}>
                          €12,450
                        </div>
                        <div className="text-gray-300 text-sm">Vendite</div>
                      </div>
                      <div className="card-aurora p-6 text-center">
                        <div className="text-3xl font-bold" style={{ color: 'var(--ai-aura)' }}>
                          89%
                        </div>
                        <div className="text-gray-300 text-sm">Soddisfazione</div>
                      </div>
                    </div>
                  </motion.div>
                )}

                {activeTab === 'design' && (
                  <motion.div
                    key="design"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    className="space-y-6"
                  >
                    <h2 className="text-xl font-semibold text-white">Design System</h2>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div className="card-aurora p-6">
                        <h3 className="text-lg font-semibold text-white mb-4">Palette Colori</h3>
                        <div className="grid grid-cols-3 gap-3">
                          <div className="text-center">
                            <div className="w-12 h-12 rounded-full mx-auto mb-2" style={{ background: 'var(--ai-aura)' }}></div>
                            <div className="text-xs text-gray-300">AI Aura</div>
                          </div>
                          <div className="text-center">
                            <div className="w-12 h-12 rounded-full mx-auto mb-2" style={{ background: 'var(--cta)' }}></div>
                            <div className="text-xs text-gray-300">CTA</div>
                          </div>
                          <div className="text-center">
                            <div className="w-12 h-12 rounded-full mx-auto mb-2" style={{ background: 'var(--accenti)' }}></div>
                            <div className="text-xs text-gray-300">Accenti</div>
                          </div>
                        </div>
                      </div>
                      <div className="card-aurora p-6">
                        <h3 className="text-lg font-semibold text-white mb-4">Effetti</h3>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-white">Glassmorphism</span>
                            <div className="w-12 h-6 bg-purple-600 rounded-full"></div>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-white">Neural Wave</span>
                            <div className="w-12 h-6 bg-purple-600 rounded-full"></div>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-white">Quantum Glow</span>
                            <div className="w-12 h-6 bg-purple-600 rounded-full"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>

          {/* Product Form Modal */}
          <ProductForm
            isOpen={isProductFormOpen}
            onClose={() => {
              setIsProductFormOpen(false);
              setEditingProduct(null);
            }}
            onSave={handleSaveProduct}
            product={editingProduct}
            isLoading={formLoading}
          />
        </motion.div>
      )}
    </AnimatePresence>
  );
}

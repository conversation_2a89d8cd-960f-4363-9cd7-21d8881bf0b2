import { getDatabase, COLLECTIONS } from './mongodb';
import { Product } from '@/types';

export async function initializeDatabase() {
  try {
    const db = await getDatabase();
    
    // Check if products already exist
    const productCount = await db.collection(COLLECTIONS.PRODUCTS).countDocuments();
    
    if (productCount === 0) {
      console.log('Initializing database with mock products...');
      
      const mockProducts: Product[] = [
        {
          name: "Bot Telegram AI",
          description: "Assistente AI personalizzato per Telegram che automatizza le tue conversazioni e gestisce le richieste dei clienti 24/7. Integrazione completa con OpenAI e funzionalità avanzate di natural language processing. Include dashboard di controllo, analytics avanzati e personalizzazione completa del comportamento.",
          price: 299,
          slug: "bot-telegram-ai",
          image_url: "/images/telegram-bot.jpg",
          tags: ["ai", "automation", "telegram", "chatbot", "customer-service", "nlp"],
          created_at: new Date()
        },
        {
          name: "Landing Page Portfolio",
          description: "Sito vetrina professionale responsive con design moderno, ottimizzato SEO e integrazione CMS. Include animazioni fluide, contact forms avanzati, analytics integrati e sistema di gestione contenuti. Perfetto per freelancer, agenzie e professionisti che vogliono una presenza online di impatto.",
          price: 599,
          slug: "landing-page-portfolio",
          image_url: "/images/portfolio-landing.jpg", 
          tags: ["web-design", "portfolio", "responsive", "seo", "cms", "landing-page"],
          created_at: new Date()
        },
        {
          name: "Automazione Notion + Google Sheets",
          description: "Sistema di workflow automation che sincronizza automaticamente i tuoi dati tra Notion e Google Sheets. Include trigger personalizzati, report automatici, dashboard real-time e integrazione con oltre 50 servizi esterni. Perfetto per team che vogliono ottimizzare la produttività.",
          price: 199,
          slug: "automazione-notion-sheets",
          image_url: "/images/notion-automation.jpg",
          tags: ["automation", "notion", "google-sheets", "workflow", "productivity", "integration"],
          created_at: new Date()
        }
      ];

      await db.collection(COLLECTIONS.PRODUCTS).insertMany(mockProducts);
      console.log('✅ Mock products initialized successfully');
      
      // Create indexes for better performance
      await db.collection(COLLECTIONS.PRODUCTS).createIndex({ slug: 1 }, { unique: true });
      await db.collection(COLLECTIONS.PRODUCTS).createIndex({ tags: 1 });
      await db.collection(COLLECTIONS.PRODUCTS).createIndex({ created_at: -1 });
      
      await db.collection(COLLECTIONS.MESSAGES).createIndex({ session_id: 1 });
      await db.collection(COLLECTIONS.MESSAGES).createIndex({ timestamp: -1 });
      
      await db.collection(COLLECTIONS.USER_PROFILES).createIndex({ session_id: 1 }, { unique: true });
      await db.collection(COLLECTIONS.USER_PROFILES).createIndex({ last_seen: -1 });
      
      await db.collection(COLLECTIONS.ORDERS).createIndex({ stripe_session_id: 1 }, { unique: true });
      await db.collection(COLLECTIONS.ORDERS).createIndex({ created_at: -1 });
      
      console.log('✅ Database indexes created successfully');
    } else {
      console.log('✅ Database already initialized with products');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Failed to initialize database:', error);
    return false;
  }
}

// Function to seed additional data if needed
export async function seedDevelopmentData() {
  try {
    const db = await getDatabase();
    
    // Add some sample user profiles for testing
    const sampleProfiles = [
      {
        session_id: 'dev_session_1',
        interests: ['ai', 'automation', 'web-design'],
        past_requests: [
          { request: 'Voglio un bot per Telegram', timestamp: new Date() },
          { request: 'Mi serve un sito portfolio', timestamp: new Date() }
        ],
        preferences: {
          communication_style: 'casual',
          product_categories: ['automation', 'web-design'],
          price_range: 'premium'
        },
        interaction_count: 5,
        last_seen: new Date(),
        emotional_profile: {
          current_mood: 'curiosity',
          engagement_level: 0.8,
          conversation_depth: 'detailed'
        }
      }
    ];
    
    for (const profile of sampleProfiles) {
      await db.collection(COLLECTIONS.USER_PROFILES).updateOne(
        { session_id: profile.session_id },
        { $setOnInsert: profile },
        { upsert: true }
      );
    }
    
    console.log('✅ Development data seeded successfully');
    return true;
  } catch (error) {
    console.error('❌ Failed to seed development data:', error);
    return false;
  }
}

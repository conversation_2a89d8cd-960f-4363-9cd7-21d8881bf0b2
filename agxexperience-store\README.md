# 🚀 AGXexperience Store - AI-Driven E-commerce

Il primo eCommerce AI-driven che ti ascolta, ti capisce, e ti guida. Un negozio minimal, elegante, immersivo. Sembra una console. Ma è il futuro dello shopping.

## ✨ Caratteristiche

- **AURORA AI Assistant** - Assistente virtuale con personalità empatica e intelligente
- **Design Neo-Glass + Dreamwave** - Interfaccia futuristica con effetti glassmorphism
- **Sistema Memoria AI** - Profiling utente e personalizzazione dell'esperienza
- **Audio-Reactive Background** - Visualizzazioni che reagiscono alla voce AI (432Hz)
- **Emotional State Engine** - Adattamento atmosferico basato sul mood della conversazione
- **Integrazione Stripe** - Pagamenti sicuri e checkout ottimizzato
- **MongoDB Database** - Gestione prodotti, utenti e conversazioni

## 🛠️ Stack Tecnologico

- **Frontend**: Next.js 15 + TypeScript + Tailwind CSS
- **Animazioni**: Framer Motion + Three.js
- **AI**: Together.ai + LLaMA 3.3-70B-Instruct-Turbo
- **Database**: MongoDB
- **Pagamenti**: Stripe
- **Hosting**: Vercel

## 🚀 Setup Rapido

### 1. Clona il repository
```bash
git clone <repository-url>
cd agxexperience-store
```

### 2. Installa le dipendenze
```bash
npm install
# oppure
yarn install
```

### 3. Configura le variabili d'ambiente
Crea un file `.env.local` nella root del progetto:

```env
# Together.ai API Configuration
TOGETHER_API_KEY=b4e1dd7a61cc0f0acf05f86b8d7fbe6c1648e6850f9fd2db5a32facb2f87c6de
TOGETHER_MODEL=meta-llama/Llama-3.3-70B-Instruct-Turbo-Free
TOGETHER_ENDPOINT=https://api.together.ai/models/meta-llama/Llama-3.3-70B-Instruct-Turbo-Free

# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_51QowemRpcWkTwq861qICgYeHofgF0IG9BcRA7RyuRhDTTLnm4BdpCcX4qiIoljFyuh7CimtpY9SEF8DQcqXJf4Ur00T2M9K2pN
STRIPE_SECRET_KEY=your_stripe_secret_key_here

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/agxexperience
MONGODB_DB=agxexperience

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret_here

# Audio Configuration (432Hz)
AUDIO_FREQUENCY=432
```

### 4. Avvia MongoDB
Assicurati che MongoDB sia in esecuzione localmente o configura un cluster MongoDB Atlas.

### 5. Inizializza il database
```bash
npm run dev
```

Poi visita: `http://localhost:3000/api/init` (POST request) per inizializzare il database con i prodotti mock.

### 6. Avvia l'applicazione
```bash
npm run dev
```

Apri [http://localhost:3000](http://localhost:3000) nel browser.

## 📱 Struttura del Progetto

```
agxexperience-store/
├── src/
│   ├── app/                    # App Router (Next.js 13+)
│   │   ├── api/               # API Routes
│   │   │   ├── chat/          # Chat AI endpoint
│   │   │   ├── products/      # Products CRUD
│   │   │   ├── checkout/      # Stripe integration
│   │   │   └── init/          # Database initialization
│   │   ├── product/[slug]/    # Dynamic product pages
│   │   ├── success/           # Payment success page
│   │   └── page.tsx           # Homepage
│   ├── components/            # React Components
│   │   ├── AuroraChat.tsx     # Main chat interface
│   │   ├── AuroraAvatar.tsx   # 3D AI avatar
│   │   └── ProductCard.tsx    # Product display
│   ├── lib/                   # Utilities
│   │   ├── mongodb.ts         # Database connection
│   │   └── init-db.ts         # Database initialization
│   └── types/                 # TypeScript definitions
└── public/                    # Static assets
```

## 🎨 Design System

### Color Palette
- **Aurora Black**: `#0D0D0D` - Background principale
- **Aurora Charcoal**: `#181818` - Container
- **Aurora Purple**: `#8E2DE2` - Elementi AI
- **Aurora Gold**: `#D4AF37` - Accenti hover
- **Aurora Teal**: `#00D8B6` - CTA buttons

### Emotion States
- **Curiosity**: Blu-viola, animazioni fluide
- **Excitement**: Oro intenso, pulsazioni rapide
- **Focus**: Palette scura, animazioni minimali
- **Discovery**: Verde-teal, effetti particellari

## 🤖 AURORA AI

AURORA (Artificial Understanding & Responsive Optimization for Retail Assistance) è l'assistente AI del progetto con:

- **Personalità Empatica**: Comprende le emozioni dell'utente
- **Memoria Conversazionale**: Ricorda interazioni passate
- **Adattamento Emotivo**: Cambia stile in base al mood
- **Suggerimenti Intelligenti**: Propone prodotti pertinenti

## 🛒 Prodotti Mock

Il sistema include 3 prodotti di esempio:

1. **Bot Telegram AI** (€299) - Assistente personalizzato
2. **Landing Page Portfolio** (€599) - Sito vetrina professionale
3. **Automazione Notion + Google Sheets** (€199) - Workflow automation

## 🔧 API Endpoints

- `GET/POST /api/products` - Gestione prodotti
- `POST /api/chat` - Conversazioni con AURORA
- `POST /api/checkout` - Creazione sessioni Stripe
- `POST /api/init` - Inizializzazione database

## 🚀 Deploy su Vercel

1. Connetti il repository a Vercel
2. Configura le variabili d'ambiente
3. Deploy automatico ad ogni push

## 📊 Metriche di Successo

- **Performance**: Lighthouse score > 90
- **Conversion Rate**: > 3% visitor-to-purchase
- **AI Engagement**: > 80% utenti interagiscono con chat
- **Session Duration**: > 2 minuti media

## 🤝 Contribuire

1. Fork del repository
2. Crea un branch per la feature (`git checkout -b feature/AmazingFeature`)
3. Commit delle modifiche (`git commit -m 'Add some AmazingFeature'`)
4. Push al branch (`git push origin feature/AmazingFeature`)
5. Apri una Pull Request

## 📄 Licenza

Questo progetto è sotto licenza MIT. Vedi il file `LICENSE` per dettagli.

## 🆘 Supporto

Per supporto tecnico o domande:
- Email: <EMAIL>
- GitHub Issues: [Apri un issue](https://github.com/your-repo/issues)

---

*Progetto sviluppato con ❤️ dal team AGXexperience*

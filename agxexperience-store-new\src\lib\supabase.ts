import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// Client per operazioni pubbliche (frontend)
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Client per operazioni admin (backend API)
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Database helper functions
export async function getSupabaseClient() {
  return supabaseAdmin;
}

// Table names
export const TABLES = {
  PRODUCTS: 'products',
  CART: 'cart',
  ORDERS: 'orders',
  USERS: 'users'
} as const;

// Types for Supabase tables
export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  slug: string;
  tags: string[];
  created_at: string;
  updated_at: string;
}

export interface CartItem {
  id: string;
  session_id: string;
  product_id: string;
  quantity: number;
  created_at: string;
  updated_at: string;
  // Relations
  product?: Product;
}

export interface Cart {
  session_id: string;
  items: CartItem[];
  total: number;
  created_at: string;
  updated_at: string;
}

export interface Order {
  id: string;
  session_id: string;
  stripe_payment_intent_id?: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  total: number;
  items: any[];
  created_at: string;
  updated_at: string;
}

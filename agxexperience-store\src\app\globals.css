@import "tailwindcss";

:root {
  /* AGXexperience Color Palette */
  --aurora-black: #0D0D0D;
  --aurora-charcoal: #181818;
  --aurora-purple: #8E2DE2;
  --aurora-gold: #D4AF37;
  --aurora-teal: #00D8B6;
  --aurora-champagne: #F7E9D7;
  --aurora-deep-blue: #1C1F4A;

  /* Emotion Colors */
  --curiosity: #3B82F6;
  --excitement: #F59E0B;
  --focus: #6B7280;
  --discovery: #10B981;

  /* Audio Frequency */
  --frequency-432hz: 432;
}

@theme inline {
  --color-background: var(--aurora-black);
  --color-foreground: #FFFFFF;
  --font-sans: var(--font-inter);
  --font-mono: var(--font-poppins);
}

body {
  background: linear-gradient(135deg, var(--aurora-black) 0%, #111111 50%, var(--aurora-charcoal) 100%);
  color: #FFFFFF;
  font-family: var(--font-inter), system-ui, sans-serif;
  overflow-x: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--aurora-charcoal);
}

::-webkit-scrollbar-thumb {
  background: var(--aurora-purple);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--aurora-gold);
}

/* Glass Morphism Effect */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Neural Wave Animation */
@keyframes neural-wave {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1) rotate(180deg);
    opacity: 0.7;
  }
  100% {
    transform: scale(1) rotate(360deg);
    opacity: 0.3;
  }
}

.neural-wave {
  animation: neural-wave 3s ease-in-out infinite;
}

/* 432Hz Frequency Pulse */
@keyframes frequency-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(142, 45, 226, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(142, 45, 226, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(142, 45, 226, 0);
  }
}

.frequency-pulse {
  animation: frequency-pulse 2s infinite;
}

/* Liquid Glass Formation */
@keyframes liquid-glass {
  0% {
    border-radius: 50%;
    transform: scale(0.8);
  }
  50% {
    border-radius: 25%;
    transform: scale(1.1);
  }
  100% {
    border-radius: 12px;
    transform: scale(1);
  }
}

.liquid-glass {
  animation: liquid-glass 1.5s ease-out forwards;
}

/* Consciousness Awakening */
@keyframes consciousness-awakening {
  0% {
    opacity: 0;
    transform: scale(0.5);
    filter: blur(20px);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
    filter: blur(5px);
  }
  100% {
    opacity: 1;
    transform: scale(1);
    filter: blur(0px);
  }
}

.consciousness-awakening {
  animation: consciousness-awakening 2s ease-out forwards;
}

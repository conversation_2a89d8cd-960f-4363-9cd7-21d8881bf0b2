'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Product } from '@/types';
import Link from 'next/link';
import { useParams } from 'next/navigation';

export default function ProductDetailPage() {
  const params = useParams();
  const slug = params.slug as string;
  
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (slug) {
      fetchProduct();
    }
  }, [slug]);

  const fetchProduct = async () => {
    try {
      const response = await fetch('/api/products');
      const data = await response.json();
      
      if (data.success) {
        const foundProduct = data.data.find((p: Product) => p.slug === slug);
        if (foundProduct) {
          setProduct(foundProduct);
        } else {
          setError('Prodotto non trovato');
        }
      } else {
        setError(data.error || 'Failed to fetch product');
      }
    } catch (err) {
      setError('Network error');
    } finally {
      setLoading(false);
    }
  };

  const handleCheckout = async () => {
    if (!product?._id) return;
    
    try {
      const response = await fetch('/api/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ product_id: product._id }),
      });

      const data = await response.json();
      
      if (data.success && data.data.checkout_url) {
        window.location.href = data.data.checkout_url;
      } else {
        alert('Errore durante il checkout: ' + (data.error || 'Unknown error'));
      }
    } catch (error) {
      alert('Errore di rete durante il checkout');
    }
  };

  const getProductIcon = (slug: string) => {
    if (slug.includes('telegram')) return '🤖';
    if (slug.includes('portfolio')) return '🌐';
    if (slug.includes('notion')) return '⚡';
    return '💎';
  };

  const getProductFeatures = (slug: string) => {
    if (slug.includes('telegram')) {
      return [
        'AI conversazionale avanzata',
        'Integrazione OpenAI GPT',
        'Dashboard di controllo',
        'Analytics dettagliati',
        'Personalizzazione completa',
        'Supporto 24/7'
      ];
    }
    if (slug.includes('portfolio')) {
      return [
        'Design responsive moderno',
        'SEO ottimizzato',
        'CMS integrato',
        'Animazioni fluide',
        'Contact forms avanzati',
        'Analytics integrati'
      ];
    }
    if (slug.includes('notion')) {
      return [
        'Sincronizzazione automatica',
        'Trigger personalizzati',
        'Report automatici',
        'Dashboard real-time',
        'Integrazione 50+ servizi',
        'Workflow ottimizzati'
      ];
    }
    return ['Funzionalità premium', 'Supporto dedicato', 'Aggiornamenti gratuiti'];
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-400 mb-4">Prodotto non trovato</h1>
          <p className="text-gray-300 mb-6">{error}</p>
          <Link href="/products" className="btn-aurora">
            Torna ai Prodotti
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      {/* Header */}
      <motion.header
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="p-6 border-b border-gray-800/50"
      >
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <Link href="/" className="text-2xl font-bold text-aurora">
            AGXexperience Store
          </Link>
          <nav className="flex space-x-6">
            <Link href="/" className="text-gray-300 hover:text-white transition-colors">
              Home
            </Link>
            <Link href="/products" className="text-gray-300 hover:text-white transition-colors">
              Prodotti
            </Link>
          </nav>
        </div>
      </motion.header>

      {/* Product Detail */}
      <div className="max-w-7xl mx-auto px-6 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          {/* Product Image */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            <div className="w-full h-96 bg-gradient-to-br from-purple-600/20 to-blue-600/20 rounded-2xl flex items-center justify-center card-aurora">
              <div className="text-9xl opacity-70">
                {getProductIcon(product.slug)}
              </div>
            </div>
            
            {/* Tags */}
            <div className="flex flex-wrap gap-3">
              {product.tags.map((tag) => (
                <span
                  key={tag}
                  className="px-3 py-2 bg-purple-600/20 text-purple-300 text-sm rounded-full border border-purple-500/30"
                >
                  {tag}
                </span>
              ))}
            </div>
          </motion.div>

          {/* Product Info */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-8"
          >
            <div>
              <h1 className="text-4xl lg:text-5xl font-bold text-white mb-4">
                {product.name}
              </h1>
              <div className="text-3xl font-bold text-aurora mb-6">
                €{product.price}
              </div>
              <p className="text-gray-300 text-lg leading-relaxed">
                {product.description}
              </p>
            </div>

            {/* Features */}
            <div>
              <h3 className="text-2xl font-bold text-white mb-4">Caratteristiche</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {getProductFeatures(product.slug).map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center space-x-3"
                  >
                    <div className="w-2 h-2 bg-aurora rounded-full"></div>
                    <span className="text-gray-300">{feature}</span>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Actions */}
            <div className="space-y-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleCheckout}
                className="w-full btn-aurora text-lg py-4"
              >
                Acquista Ora - €{product.price}
              </motion.button>
              
              <div className="flex space-x-4">
                <Link
                  href="/products"
                  className="flex-1 px-6 py-3 bg-gray-700/50 text-white text-center rounded-lg hover:bg-gray-600/50 transition-colors"
                >
                  Altri Prodotti
                </Link>
                <Link
                  href="/"
                  className="flex-1 px-6 py-3 bg-gray-700/50 text-white text-center rounded-lg hover:bg-gray-600/50 transition-colors"
                >
                  Chatta con AURORA
                </Link>
              </div>
            </div>

            {/* Trust Indicators */}
            <div className="card-aurora">
              <div className="flex items-center justify-between text-sm text-gray-300">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  <span>Pagamento sicuro</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
                  <span>Supporto 24/7</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-purple-400 rounded-full"></div>
                  <span>Garanzia 30 giorni</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Footer */}
      <footer className="py-8 px-6 border-t border-gray-800/50">
        <div className="max-w-7xl mx-auto text-center">
          <p className="text-gray-500">
            © 2025 AGXexperience Store - Powered by AURORA AI
          </p>
        </div>
      </footer>
    </div>
  );
}

import { MongoClient, Db } from 'mongodb';

if (!process.env.MONGODB_URI) {
  throw new Error('Please add your MongoDB URI to .env.local');
}

const uri = process.env.MONGODB_URI;

const options = {
  // Configurazione minimale per MongoDB Atlas (lascia che gestisca SSL automaticamente)
  serverSelectionTimeoutMS: 10000,
  socketTimeoutMS: 60000,
  connectTimeoutMS: 15000,
  maxPoolSize: 10,
  minPoolSize: 1,
  retryWrites: true,
  retryReads: true,
};

let client: MongoClient;
let clientPromise: Promise<MongoClient>;

if (process.env.NODE_ENV === 'development') {
  // In development mode, use a global variable so that the value
  // is preserved across module reloads caused by HMR (Hot Module Replacement).
  let globalWithMongo = global as typeof globalThis & {
    _mongoClientPromise?: Promise<MongoClient>;
  };

  if (!globalWithMongo._mongoClientPromise) {
    client = new MongoClient(uri, options);
    globalWithMongo._mongoClientPromise = client.connect();
  }
  clientPromise = globalWithMongo._mongoClientPromise;
} else {
  // In production mode, it's best to not use a global variable.
  client = new MongoClient(uri, options);
  clientPromise = client.connect();
}

// Export a module-scoped MongoClient promise. By doing this in a
// separate module, the client can be shared across functions.
export default clientPromise;

// Database helper functions with error handling
export async function getDatabase(): Promise<Db> {
  try {
    const client = await clientPromise;
    return client.db(process.env.MONGODB_DB || 'agxexperience');
  } catch (error) {
    console.error('MongoDB connection error:', error);

    // Check if it's an SSL error
    if (error instanceof Error && error.message.includes('SSL')) {
      console.log('🔧 SSL Error detected. Consider using a local MongoDB instance without SSL for development.');
    }

    throw new Error('Failed to connect to database. Please check your MongoDB configuration.');
  }
}

// Collections names
export const COLLECTIONS = {
  PRODUCTS: 'products',
  USERS: 'users',
  MESSAGES: 'messages',
  ORDERS: 'orders',
  USER_PROFILES: 'user_profiles',
  CARTS: 'carts'
} as const;

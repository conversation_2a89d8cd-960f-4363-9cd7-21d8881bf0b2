// Simple test script to initialize database
const fetch = require('node-fetch');

async function testInit() {
  try {
    console.log('🚀 Testing database initialization...');
    
    const response = await fetch('http://localhost:3000/api/init', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    const data = await response.json();
    console.log('Response:', data);
    
    if (data.success) {
      console.log('✅ Database initialized successfully!');
    } else {
      console.log('❌ Database initialization failed:', data.error);
    }
  } catch (error) {
    console.error('❌ Error testing initialization:', error.message);
  }
}

testInit();

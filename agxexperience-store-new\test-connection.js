// Test MongoDB Connection - Simple
require('dotenv').config({ path: '.env.local' });

console.log('🔍 Testing MongoDB Connection...');
console.log('URI:', process.env.MONGODB_URI);
console.log('Database:', process.env.MONGODB_DB);

// Test MongoDB Atlas connection
const atlasUri = process.env.MONGODB_URI;
console.log('Atlas URI:', atlasUri);

async function testConnection() {
  try {
    const { MongoClient } = require('mongodb');
    const client = new MongoClient(atlasUri);
    
    console.log('📡 Attempting connection...');
    await client.connect();
    console.log('✅ Connected successfully!');
    
    const db = client.db('agxexperience');
    const collections = await db.listCollections().toArray();
    console.log('📁 Collections:', collections.map(c => c.name));
    
    await client.close();
    console.log('✅ Connection test completed!');
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
  }
}

testConnection();

'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface HeaderProps {
  onCartOpen: () => void;
  onProfileOpen: () => void;
  cartItemCount: number;
}

export default function Header({ onCartOpen, onProfileOpen, cartItemCount }: HeaderProps) {
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <motion.header
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`fixed top-0 left-0 right-0 z-20 transition-all duration-300 ${
        scrolled ? 'glass-strong border-b border-white/10' : 'bg-transparent'
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 py-3 sm:py-4">
        <div className="flex items-center justify-between">
          {/* Favicon */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            className="flex items-center"
          >
            <div
              className="w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center consciousness-awakening"
              style={{ background: 'linear-gradient(135deg, var(--ai-aura), var(--cta))' }}
            >
              <span className="text-white font-bold text-sm sm:text-lg">A</span>
            </div>
          </motion.div>

          {/* Actions - Responsive */}
          <div className="flex items-center space-x-2 sm:space-x-4">
            {/* Profile Button */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onProfileOpen}
              className="relative w-8 h-8 sm:w-10 sm:h-10 glass border border-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/10 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </motion.button>

            {/* Cart Button */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onCartOpen}
              className="relative w-8 h-8 sm:w-10 sm:h-10 glass border border-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/10 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
              </svg>
              
              {/* Cart Badge */}
              {cartItemCount > 0 && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="absolute -top-2 -right-2 w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold text-white"
                  style={{ background: 'var(--aurora-teal)' }}
                >
                  {cartItemCount > 9 ? '9+' : cartItemCount}
                </motion.div>
              )}
            </motion.button>

            {/* 432Hz Indicator */}
            <div className="hidden md:flex items-center space-x-2 glass px-3 py-1 rounded-full border border-white/20">
              <div 
                className="w-2 h-2 rounded-full frequency-pulse"
                style={{ backgroundColor: 'var(--aurora-gold)' }}
              ></div>
              <span className="text-xs font-medium" style={{ color: 'var(--aurora-gold)' }}>
                432Hz
              </span>
            </div>
          </div>
        </div>
      </div>
    </motion.header>
  );
}

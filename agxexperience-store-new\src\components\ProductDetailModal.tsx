'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Product } from '@/types';

interface ProductDetailModalProps {
  product: Product | null;
  isOpen: boolean;
  onClose: () => void;
  onPurchase: (productId: string) => void;
  onAddToCart?: (productId: string) => void;
}

export default function ProductDetailModal({
  product,
  isOpen,
  onClose,
  onPurchase,
  onAddToCart
}: ProductDetailModalProps) {
  const [isLoading, setIsLoading] = useState(false);

  if (!product) return null;

  const getProductIcon = (slug: string) => {
    if (slug.includes('telegram')) return '🤖';
    if (slug.includes('portfolio')) return '🌐';
    if (slug.includes('notion')) return '⚡';
    return '💎';
  };

  const getProductFeatures = (slug: string) => {
    if (slug.includes('telegram')) {
      return [
        'AI conversazionale avanzata con GPT',
        'Dashboard di controllo completa',
        'Analytics dettagliati in tempo reale',
        'Personalizzazione totale del comportamento',
        'Integrazione con sistemi esterni',
        'Supporto tecnico dedicato 24/7'
      ];
    }
    if (slug.includes('portfolio')) {
      return [
        'Design responsive ultra-moderno',
        'SEO ottimizzato per Google',
        'CMS integrato per gestione contenuti',
        'Animazioni fluide e interattive',
        'Contact forms avanzati',
        'Analytics e tracking integrati'
      ];
    }
    if (slug.includes('notion')) {
      return [
        'Sincronizzazione automatica bidirezionale',
        'Trigger personalizzati per automazioni',
        'Report automatici e dashboard real-time',
        'Integrazione con 50+ servizi esterni',
        'Workflow ottimizzati per produttività',
        'Backup automatico e sicurezza dati'
      ];
    }
    return ['Funzionalità premium avanzate', 'Supporto dedicato', 'Aggiornamenti gratuiti'];
  };

  const handlePurchase = async () => {
    setIsLoading(true);
    try {
      await onPurchase(product._id!);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="glass-strong border border-white/20 rounded-xl max-w-2xl w-full max-h-[80vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header Compatto con Pulsanti Rapidi */}
            <div className="sticky top-0 glass-strong border-b border-white/10 p-4 flex items-center justify-between">
              <div>
                <h2 className="text-lg font-bold text-white">Dettagli Prodotto</h2>
                <p className="text-purple-300 text-xs">Powered by AURORA AI</p>
              </div>

              {/* Pulsanti Rapidi + Chiusura */}
              <div className="flex items-center space-x-2">
                {/* Pulsante Carrello Rapido */}
                {onAddToCart && (
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => onAddToCart(product._id!)}
                    className="w-8 h-8 glass border border-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/10 transition-colors"
                    title="Aggiungi al Carrello"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                    </svg>
                  </motion.button>
                )}

                {/* Pulsante Acquista Rapido */}
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handlePurchase}
                  disabled={isLoading}
                  className="w-8 h-8 rounded-full flex items-center justify-center text-white transition-colors disabled:opacity-50"
                  style={{
                    background: 'linear-gradient(135deg, var(--aurora-purple), var(--aurora-teal))'
                  }}
                  title={`Acquista Ora - €${product.price}`}
                >
                  {isLoading ? (
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      className="w-3 h-3 border-2 border-white border-t-transparent rounded-full"
                    />
                  ) : (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  )}
                </motion.button>

                {/* Pulsante Chiusura */}
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={onClose}
                  className="w-8 h-8 bg-gray-700/50 rounded-full flex items-center justify-center text-white hover:bg-gray-600/50 transition-colors"
                  title="Chiudi"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </motion.button>
              </div>
            </div>

            <div className="p-4 max-h-[calc(80vh-80px)] overflow-y-auto">
              <div className="space-y-4">
                {/* Product Image Compatta */}
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                  className="flex items-center space-x-4"
                >
                  <div className="relative w-24 h-24 bg-gradient-to-br from-purple-600/20 to-blue-600/20 rounded-xl flex items-center justify-center overflow-hidden card-aurora flex-shrink-0">
                    <motion.div
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      className="text-4xl opacity-70"
                    >
                      {getProductIcon(product.slug)}
                    </motion.div>

                    {/* Neural Wave Background */}
                    <div className="absolute inset-0 neural-wave opacity-10 bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-xl"></div>

                    {/* 432Hz Indicator */}
                    <div className="absolute bottom-1 left-1">
                      <div className="flex items-center space-x-1 glass px-2 py-1 rounded-full">
                        <div className="w-1 h-1 bg-yellow-400 rounded-full frequency-pulse"></div>
                        <span className="text-yellow-400 text-xs font-medium">432Hz</span>
                      </div>
                    </div>
                  </div>

                  {/* Info Prodotto Inline */}
                  <div className="flex-1">
                    <h1 className="text-xl font-bold text-white mb-2">
                      {product.name}
                    </h1>
                    <div className="flex items-center space-x-3 mb-2">
                      <span
                        className="text-2xl font-bold frequency-pulse"
                        style={{
                          color: 'var(--aurora-teal)',
                          textShadow: '0 0 20px rgba(0, 216, 182, 0.4)'
                        }}
                      >
                        €{product.price}
                      </span>
                      <div className="flex items-center space-x-1">
                        <div
                          className="w-2 h-2 rounded-full animate-pulse"
                          style={{ backgroundColor: 'var(--aurora-teal)' }}
                        ></div>
                        <span style={{ color: 'var(--aurora-teal)' }} className="text-xs">Disponibile</span>
                      </div>
                    </div>

                    {/* Tags Compatti */}
                    <div className="flex flex-wrap gap-1">
                      {product.tags.slice(0, 3).map((tag, index) => (
                        <span
                          key={tag}
                          className="px-2 py-1 bg-purple-600/20 text-purple-300 text-xs rounded-full border border-purple-500/30"
                        >
                          {tag}
                        </span>
                      ))}
                      {product.tags.length > 3 && (
                        <span className="px-2 py-1 bg-gray-600/20 text-gray-400 text-xs rounded-full">
                          +{product.tags.length - 3}
                        </span>
                      )}
                    </div>
                  </div>
                </motion.div>

                {/* Descrizione */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="space-y-3"
                >
                  <p className="text-gray-300 text-sm leading-relaxed">
                    {product.description}
                  </p>

                  {/* Features Compatte */}
                  <div>
                    <h3 className="text-sm font-bold text-white mb-2">Caratteristiche Principali</h3>
                    <div className="grid grid-cols-1 gap-1">
                      {getProductFeatures(product.slug).slice(0, 4).map((feature, index) => (
                        <div
                          key={index}
                          className="flex items-start space-x-2"
                        >
                          <div className="w-1.5 h-1.5 bg-aurora rounded-full mt-1.5 flex-shrink-0"></div>
                          <span className="text-gray-300 text-xs">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Trust Indicators Compatti */}
                  <div className="flex justify-center space-x-4 text-center">
                    <div className="flex items-center space-x-1">
                      <div className="w-4 h-4 bg-green-500/20 rounded-full flex items-center justify-center">
                        <svg className="w-2 h-2 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <span className="text-green-400 text-xs">Sicuro</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-4 h-4 bg-blue-500/20 rounded-full flex items-center justify-center">
                        <svg className="w-2 h-2 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z" />
                        </svg>
                      </div>
                      <span className="text-blue-400 text-xs">24/7</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-4 h-4 bg-purple-500/20 rounded-full flex items-center justify-center">
                        <svg className="w-2 h-2 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <span className="text-purple-400 text-xs">Garanzia</span>
                    </div>
                  </div>

                  {/* Action Buttons Compatti */}
                  <div className="flex space-x-2">
                    {/* Add to Cart Button */}
                    {onAddToCart && (
                      <button
                        onClick={() => onAddToCart(product._id!)}
                        className="flex-1 py-2 px-3 glass border border-white/20 rounded-lg text-white hover:bg-white/10 transition-colors text-sm"
                      >
                        Carrello
                      </button>
                    )}

                    {/* Purchase Button */}
                    <button
                      onClick={handlePurchase}
                      disabled={isLoading}
                      className={`${onAddToCart ? 'flex-1' : 'w-full'} py-2 px-3 rounded-lg text-sm text-white transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed`}
                      style={{
                        background: 'linear-gradient(135deg, var(--aurora-purple), var(--aurora-teal))',
                        color: 'var(--aurora-champagne)'
                      }}
                    >
                      {isLoading ? (
                        <div className="flex items-center justify-center space-x-1">
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                            className="w-3 h-3 border-2 border-white border-t-transparent rounded-full"
                          />
                          <span>...</span>
                        </div>
                      ) : (
                        `Acquista €${product.price}`
                      )}
                    </button>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

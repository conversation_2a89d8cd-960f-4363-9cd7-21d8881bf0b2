{"name": "agxexperience-store", "version": "1.0.0", "private": true, "description": "Il primo eCommerce AI-driven che ti ascolta, ti capisce, e ti guida", "author": "AGXexperience Team", "keywords": ["ecommerce", "ai", "nextjs", "aurora", "stripe", "mongodb"], "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "init-db": "node test-init.js", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "next": "15.4.3", "framer-motion": "^10.16.0", "mongodb": "^6.3.0", "stripe": "^14.15.0", "@stripe/stripe-js": "^2.4.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.4.3", "@eslint/eslintrc": "^3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}